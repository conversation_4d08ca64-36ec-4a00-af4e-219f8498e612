"""
固定提示词矩阵配置
为所有载具×天气×场景组合提供预定义的完整提示词
"""

from typing import Dict, List, Tuple, Any

# 固定提示词矩阵 - 3种载具 × 4种天气 × 3种场景 = 36种组合
FIXED_PROMPT_MATRIX = {
    # 坦克 - 雨天
    ("坦克", "雨天", "城市"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, detailed armor plating, tracks, turret, wet metal surfaces, rain droplets, urban street, heavy rain, blurred city background",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, sunny weather, dry"
    },
    ("坦克", "雨天", "岛屿"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, muddy armor, rocky shore, tropical storm, heavy rain, stormy ocean background",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, sunny weather, calm sea"
    },
    ("坦克", "雨天", "乡村"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, mud-covered tracks, rain-soaked field, storm clouds, rural landscape background",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, sunny weather, dry field"
    },
    
    # 坦克 - 雪天
    ("坦克", "雪天", "城市"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, winter camouflage, snow-covered armor, frost on metal, snowy city street, blizzard",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, summer weather, warm"
    },
    ("坦克", "雪天", "岛屿"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, arctic camouflage, ice crystals on cannon, frozen island, snow-covered terrain",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, tropical weather, warm"
    },
    ("坦克", "雪天", "乡村"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, winter camouflage, snow on armor, exhaust vapor, snowy countryside",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, summer weather, warm"
    },
    
    # 坦克 - 大雾
    ("坦克", "大雾", "城市"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, headlights on, emerging from fog, thick mist, foggy city",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, clear weather, sunny"
    },
    ("坦克", "大雾", "岛屿"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, condensation on armor, thick sea fog, misty island coastline",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, clear weather, sunny"
    },
    ("坦克", "大雾", "乡村"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, volumetric fog, misty rural landscape, heavy fog conditions",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, clear weather, sunny"
    },
    
    # 坦克 - 夜间
    ("坦克", "夜间", "城市"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, tactical lights, night mission, streetlight illumination, city lights background",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, daytime, bright"
    },
    ("坦克", "夜间", "岛屿"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, moonlight on armor, dark island, starry sky, night operations",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, daytime, bright"
    },
    ("坦克", "夜间", "乡村"): {
        "positive": "(single main battle tank:1.4), one cannon barrel, complete tank, stealth position, moonlight, dark countryside, tactical lights off",
        "negative": "(multiple tanks:1.3), (multiple cannons, double barrels:1.2), incomplete tank, missing parts, deformed, toy, cartoon, daytime, bright"
    },

    # 战机 - 雨天
    ("战机", "雨天", "城市"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, water droplets on canopy, flying over city, heavy rain, storm clouds",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, sunny weather, clear sky"
    },
    ("战机", "雨天", "岛屿"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, banking maneuver, tropical storm, island below, stormy sea",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, sunny weather, calm sea"
    },
    ("战机", "雨天", "乡村"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, low altitude flight, afterburner glow, rain clouds, rural landscape below",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, sunny weather, clear sky"
    },

    # 战机 - 雪天
    ("战机", "雪天", "城市"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, winter camouflage, snowflakes around aircraft, snowy city below",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, summer weather, warm"
    },
    ("战机", "雪天", "岛屿"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, frost on wings, frozen arctic island, icy landscape below",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, tropical weather, warm"
    },
    ("战机", "雪天", "乡村"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, contrail in cold air, snow-covered rural terrain below",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, summer weather, warm"
    },

    # 战机 - 大雾
    ("战机", "大雾", "城市"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, flying above fog layer, thick mist, city skyscrapers poking through fog",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, clear sky, sunny"
    },
    ("战机", "大雾", "岛屿"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, navigating through sea fog, dense mist, island cliffs barely visible",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, clear sky, sunny"
    },
    ("战机", "大雾", "乡村"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, emerging from fog bank, thick fog over countryside",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, clear sky, sunny"
    },

    # 战机 - 夜间
    ("战机", "夜间", "城市"): {
        "positive": "(single stealth bomber:1.4), flying wing design, complete aircraft, dark silhouette, gliding over city at night, city lights below",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, daytime, bright"
    },
    ("战机", "夜间", "岛屿"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, afterburner glow, night mission, dark island, moonlit ocean",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, daytime, bright"
    },
    ("战机", "夜间", "乡村"): {
        "positive": "(single fighter jet:1.4), two wings, one cockpit, complete aircraft, supersonic speed, afterburner streak, dark rural landscape",
        "negative": "(multiple jets:1.3), (extra wings, missing wings:1.2), incomplete aircraft, missing parts, deformed, toy, cartoon, daytime, bright"
    },

    # 舰艇 - 雨天
    ("舰艇", "雨天", "城市"): {
        "positive": "(single naval destroyer:1.4), one hull, complete warship, superstructure, radar arrays, docked in harbor, heavy storm, rain-wet surfaces, city harbor",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, sunny weather, clear sky"
    },
    ("舰艇", "雨天", "岛屿"): {
        "positive": "(single aircraft carrier:1.4), one hull, complete warship, flight deck, island superstructure, cutting through storm waves, island nearby",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, sunny weather, calm sea"
    },
    ("舰艇", "雨天", "乡村"): {
        "positive": "(single naval frigate:1.4), one hull, complete warship, patrolling coast, rain conditions, rural coastline background",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, sunny weather, clear sky"
    },

    # 舰艇 - 雪天
    ("舰艇", "雪天", "城市"): {
        "positive": "(single destroyer:1.4), one hull, complete warship, snow-covered deck, angular stealth design, frozen city harbor",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, summer weather, warm"
    },
    ("舰艇", "雪天", "岛屿"): {
        "positive": "(single submarine:1.4), one hull, complete vessel, surfacing through ice, conning tower, snowy arctic island, icy waters",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, tropical weather, warm"
    },
    ("舰艇", "雪天", "乡村"): {
        "positive": "(single destroyer:1.4), one hull, complete warship, sailing along snowy coast, winter conditions, snow-covered shoreline",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, summer weather, warm"
    },

    # 舰艇 - 大雾
    ("舰艇", "大雾", "城市"): {
        "positive": "(single aircraft carrier:1.4), one hull, complete warship, island superstructure, emerging from harbor fog, thick mist, city port",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, clear weather, sunny"
    },
    ("舰艇", "大雾", "岛屿"): {
        "positive": "(single stealth destroyer:1.4), one hull, complete warship, angular design, patrolling in sea fog, dense mist, island coastline",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, clear weather, sunny"
    },
    ("舰艇", "大雾", "乡村"): {
        "positive": "(single battleship:1.4), one hull, complete warship, gun turrets, anchored in fog, thick mist, rural coastline",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, clear weather, sunny"
    },

    # 舰艇 - 夜间
    ("舰艇", "夜间", "城市"): {
        "positive": "(single destroyer:1.4), one hull, complete warship, deck lights on, docked at night, city harbor, illuminated superstructure",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, daytime, bright"
    },
    ("舰艇", "夜间", "岛屿"): {
        "positive": "(single cruiser:1.4), one hull, complete warship, night maneuvers, ship lights, dark island, starry sky, moonlit ocean",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, daytime, bright"
    },
    ("舰艇", "夜间", "乡村"): {
        "positive": "(single submarine:1.4), one hull, complete vessel, surfaced at night, conning tower, dark rural bay, moonlight on water",
        "negative": "(multiple ships:1.3), (multiple hulls:1.2), incomplete ship, missing parts, deformed, toy, cartoon, daytime, bright"
    }
}





# 支持的选项
SUPPORTED_VEHICLES = ["坦克", "战机", "舰艇"]
SUPPORTED_WEATHER = ["雨天", "雪天", "大雾", "夜间"]
SUPPORTED_SCENES = ["城市", "岛屿", "乡村"]

def get_fixed_prompt(vehicle: str, weather: str, scene: str) -> Tuple[str, str]:
    """
    获取固定提示词

    Args:
        vehicle: 载具类型 (坦克/战机/舰艇)
        weather: 天气条件 (雨天/雪天/大雾/夜间)
        scene: 场景环境 (城市/岛屿/乡村)

    Returns:
        tuple[str, str]: (正面提示词, 负面提示词)

    Raises:
        KeyError: 当组合不存在时
        ValueError: 当参数无效时
    """
    # 验证参数
    if vehicle not in SUPPORTED_VEHICLES:
        raise ValueError(f"不支持的载具类型: {vehicle}. 支持的类型: {SUPPORTED_VEHICLES}")
    if weather not in SUPPORTED_WEATHER:
        raise ValueError(f"不支持的天气条件: {weather}. 支持的条件: {SUPPORTED_WEATHER}")
    if scene not in SUPPORTED_SCENES:
        raise ValueError(f"不支持的场景环境: {scene}. 支持的环境: {SUPPORTED_SCENES}")

    # 获取提示词
    key = (vehicle, weather, scene)
    if key not in FIXED_PROMPT_MATRIX:
        raise KeyError(f"未找到组合 {key} 的提示词")

    prompt_data = FIXED_PROMPT_MATRIX[key]
    return prompt_data["positive"], prompt_data["negative"]

def get_all_combinations() -> List[Tuple[str, str, str]]:
    """
    获取所有支持的载具×天气×场景组合

    Returns:
        list[tuple[str, str, str]]: 所有组合的列表
    """
    return list(FIXED_PROMPT_MATRIX.keys())

def validate_matrix_completeness() -> Dict[str, Any]:
    """
    验证提示词矩阵的完整性

    Returns:
        dict: 验证结果
    """
    expected_combinations = []
    for vehicle in SUPPORTED_VEHICLES:
        for weather in SUPPORTED_WEATHER:
            for scene in SUPPORTED_SCENES:
                expected_combinations.append((vehicle, weather, scene))

    actual_combinations = set(FIXED_PROMPT_MATRIX.keys())
    expected_combinations_set = set(expected_combinations)

    missing = expected_combinations_set - actual_combinations
    extra = actual_combinations - expected_combinations_set

    return {
        "total_expected": len(expected_combinations),
        "total_actual": len(actual_combinations),
        "is_complete": len(missing) == 0 and len(extra) == 0,
        "missing_combinations": list(missing),
        "extra_combinations": list(extra)
    }
