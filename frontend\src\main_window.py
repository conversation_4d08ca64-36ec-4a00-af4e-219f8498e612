import sys
import os
import asyncio
import logging
from PyQt6.QtGui import QIcon, QFont, QPixmap
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QGroupBox, QFormLayout, QLineEdit, QSpinBox, QTextEdit, QPushButton,
    QGridLayout, QFrame, QSplitter, QLabel, QProgressBar, QMessageBox,
    QRadioButton, QDoubleSpinBox, QComboBox, QStyle, QCheckBox, QScrollArea,
    QSlider
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer

from services.ai_service import AIServiceClient
from services.traditional_generation_client import TraditionalGenerationClient
from services.sd_model_service import SDModelService
from styles.unified_theme import MINIMAL_STYLE
from styles.button_styles import ButtonStyleManager
from styles.style_utils import StyleUtils
from styles.responsive_utils import ResponsiveUtils
from utils.ui_optimizer import (
    <PERSON><PERSON>ptim<PERSON>, ErrorHandler, ProgressManager,
    NotificationManager, StatusManager, ValidationHelper
)

logger = logging.getLogger(__name__)
try:
    from widgets.dataset_management_widget import DatasetManagementWidget
except ImportError:
    # 如果导入失败，创建一个占位符组件
    from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
    class DatasetManagementWidget(QWidget):
        def __init__(self, parent=None):
            super().__init__(parent)
            layout = QVBoxLayout(self)
            label = QLabel("数据管理功能暂时不可用")
            layout.addWidget(label)

class AIGenerationWorker(QThread):
    """AI生成工作线程"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号
    
    def __init__(self, ai_client, generation_params):
        super().__init__()
        self.ai_client = ai_client
        self.generation_params = generation_params
    
    def run(self):
        try:
            self.progress.emit("正在生成图像...")
            result = self.ai_client.generate_images(**self.generation_params)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))



class BatchGenerationWorker(QThread):
    """批量生成工作线程"""
    progress = pyqtSignal(str, int, int)  # 进度信息, 当前数量, 总数量
    batch_progress = pyqtSignal(int, int)  # 批次进度, 当前批次, 总批次
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号
    image_generated = pyqtSignal(dict)  # 单张图片生成完成信号
    
    def __init__(self, ai_client, batch_configs):
        super().__init__()
        self.ai_client = ai_client
        self.batch_configs = batch_configs
    
    def run(self):
        try:
            import random

            self.progress.emit("开始批量生成...", 0, len(self.batch_configs))

            # 可选项列表（用于随机选择）
            targets = ["坦克", "战机", "舰艇"]
            weathers = ["雨天", "雪天", "大雾", "夜间"]
            scenes = ["城市", "岛屿", "乡村"]

            # 预处理所有配置，解析随机参数
            processed_configs = []
            for i, config in enumerate(self.batch_configs):
                if self.isInterruptionRequested():
                    break

                # 处理随机参数 - 在每次生成时重新随机化
                actual_config = config.copy()

                # 检查是否需要随机选择目标
                if actual_config.get('military_target') == 'RANDOM':
                    actual_config['military_target'] = random.choice(targets)

                # 检查是否需要随机选择天气
                if actual_config.get('weather') == 'RANDOM':
                    actual_config['weather'] = random.choice(weathers)

                # 检查是否需要随机选择场景
                if actual_config.get('scene') == 'RANDOM':
                    actual_config['scene'] = random.choice(scenes)

                # 处理随机种子
                if actual_config.get('seed') == -1:
                    # 生成真正的随机种子
                    actual_config['seed'] = random.randint(0, 2147483647)

                processed_configs.append(actual_config)

                target = actual_config.get('military_target', '未知')
                weather = actual_config.get('weather', '未知')
                scene = actual_config.get('scene', '未知')

                self.progress.emit(f"准备第 {i+1}/{len(self.batch_configs)} 组配置 ({target}, {weather}, {scene})...", i, len(self.batch_configs))

            if self.isInterruptionRequested():
                return

            # 使用批量生成API
            self.progress.emit("开始批量生成图像...", 0, len(processed_configs))

            try:
                batch_result = self.ai_client.batch_generate(processed_configs)

                # 处理批量生成结果
                all_results = batch_result.get('results', [])
                successful_count = batch_result.get('successful_count', 0)

                # 发送每个生成结果的信号
                for i, result in enumerate(all_results):
                    if result.get('success', True):
                        self.image_generated.emit(result)
                    self.batch_progress.emit(i + 1, len(all_results))

                final_result = {
                    "results": all_results,
                    "total_configs": len(processed_configs),
                    "successful_count": successful_count,
                    "failed_count": len(processed_configs) - successful_count
                }

                self.finished.emit(final_result)

            except Exception as e:
                # 如果批量生成失败，回退到逐个生成
                self.progress.emit("批量生成失败，回退到逐个生成...", 0, len(processed_configs))

                all_results = []
                successful_count = 0

                for i, actual_config in enumerate(processed_configs):
                    if self.isInterruptionRequested():
                        break

                    self.batch_progress.emit(i + 1, len(processed_configs))

                    target = actual_config.get('military_target', '未知')
                    weather = actual_config.get('weather', '未知')
                    scene = actual_config.get('scene', '未知')

                    self.progress.emit(f"正在生成第 {i+1}/{len(processed_configs)} 组图像 ({target}, {weather}, {scene})...", i, len(processed_configs))

                    try:
                        result = self.ai_client.generate_images(**actual_config)
                        all_results.append(result)
                        successful_count += 1
                        self.image_generated.emit(result)
                    except Exception as single_e:
                        error_result = {"error": str(single_e), "config": actual_config, "success": False}
                        all_results.append(error_result)
                        self.progress.emit(f"第 {i+1} 组生成失败: {str(single_e)}", i, len(processed_configs))

                final_result = {
                    "results": all_results,
                    "total_configs": len(processed_configs),
                    "successful_count": successful_count,
                    "failed_count": len(processed_configs) - successful_count
                }

                self.finished.emit(final_result)

        except Exception as e:
            self.error.emit(str(e))


class ModelSwitchWorker(QThread):
    """模型切换工作线程"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号

    def __init__(self, ai_client, model_key):
        super().__init__()
        self.ai_client = ai_client
        self.model_key = model_key

    def run(self):
        try:
            self.progress.emit(f"正在切换到模型: {self.model_key}...")
            result = self.ai_client.switch_model(self.model_key)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class SDModelSwitchWorker(QThread):
    """SD模型切换工作线程"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号

    def __init__(self, sd_service, model_key):
        super().__init__()
        self.sd_service = sd_service
        self.model_key = model_key

    def run(self):
        try:
            self.progress.emit(f"正在加载SD权重模型: {self.model_key}...")
            result = self.sd_service.load_model(self.model_key)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class ModelPreloadWorker(QThread):
    """模型预加载工作线程"""
    progress = pyqtSignal(str)  # 进度信息
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号

    def __init__(self, ai_client, model_key):
        super().__init__()
        self.ai_client = ai_client
        self.model_key = model_key

    def run(self):
        try:
            self.progress.emit(f"正在预加载模型: {self.model_key}...")
            result = self.ai_client.preload_model(self.model_key)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class TraditionalGenerationWorker(QThread):
    """传统生成工作线程"""
    progress = pyqtSignal(str)  # 进度信息
    progress_value = pyqtSignal(int)  # 进度值 (0-100)
    finished = pyqtSignal(dict)  # 完成信号
    error = pyqtSignal(str)  # 错误信号

    def __init__(self, traditional_client, generation_params):
        super().__init__()
        self.traditional_client = traditional_client
        self.generation_params = generation_params
        self._is_cancelled = False

    def run(self):
        try:
            # 模拟进度更新
            self.progress.emit("正在初始化传统合成...")
            self.progress_value.emit(10)
            self.msleep(500)  # 短暂延迟让用户看到进度

            if self._is_cancelled:
                return

            self.progress.emit("正在选择素材...")
            self.progress_value.emit(20)
            self.msleep(300)

            if self._is_cancelled:
                return

            self.progress.emit("正在加载图像...")
            self.progress_value.emit(30)
            self.msleep(300)

            if self._is_cancelled:
                return

            self.progress.emit("正在执行抠图...")
            self.progress_value.emit(50)

            # 调用后端API
            result = self.traditional_client.generate_images(**self.generation_params)

            if self._is_cancelled:
                return

            self.progress.emit("正在合成图像...")
            self.progress_value.emit(80)
            self.msleep(300)

            if self._is_cancelled:
                return

            self.progress.emit("正在保存结果...")
            self.progress_value.emit(95)
            self.msleep(200)

            if self._is_cancelled:
                return

            self.progress.emit("传统合成完成")
            self.progress_value.emit(100)

            self.finished.emit(result)

        except Exception as e:
            if not self._is_cancelled:
                self.error.emit(str(e))

    def cancel(self):
        """取消生成"""
        self._is_cancelled = True


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("军事目标数据集生成平台 v2.0")

        # 优化窗口尺寸以适应小屏幕
        self._setup_window_size()
        self.setWindowIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ComputerIcon))

        # 应用UI优化
        UIOptimizer.setup_chinese_font(self)

        # 初始化AI服务客户端
        self.ai_client = AIServiceClient()
        self.traditional_client = TraditionalGenerationClient()
        self.sd_model_service = SDModelService()
        self.generation_worker = None
        self.batch_worker = None
        self.model_switch_worker = None
        self.model_preload_worker = None
        self.traditional_worker = None

        # 初始化UI优化组件
        self.error_handler = ErrorHandler(self)
        self.progress_manager = None  # 将在UI创建后初始化
        self.status_manager = None    # 将在UI创建后初始化

        # 用于AI prompt自动生成
        self.ai_target_radios = []
        self.ai_weather_radios = []
        self.ai_scene_radios = []
        self._current_generated_prompt = None  # 存储当前自动生成的提示词
        self._last_auto_generated_text = ""  # 存储上次自动生成的文本，用于判断是否为用户手动编辑
        self._is_updating_prompt = False  # 标记是否正在更新提示词，避免循环触发

        # 用于传统生成
        self.trad_target_radios = []
        self.trad_weather_radios = []
        self.trad_scene_radios = []

        # 图片显示相关
        self.current_images = []  # 当前显示的图片列表
        self.current_image_index = 0  # 当前显示的图片索引

        # 模型管理相关
        self.available_models = {}
        self.current_model_info = {}
        self.cached_models = []

        # 智能预加载相关
        self.preload_timer = QTimer()
        self.preload_timer.setSingleShot(True)
        self.preload_timer.timeout.connect(self._trigger_smart_preload)
        self.preload_delay = 3000  # 3秒延迟
        self.pending_preload_model = None
        self.is_preloading = False

        self._create_main_widget()
        self._initialize_ui_managers()
        self._initialize_ai_service()

    def _setup_window_size(self):
        """设置窗口尺寸以适应不同屏幕"""
        # 使用响应式工具类获取窗口尺寸
        window_width, window_height, min_width, min_height = ResponsiveUtils.get_responsive_window_size()

        # 获取屏幕尺寸用于居中
        screen_width, screen_height = ResponsiveUtils.get_screen_size()

        # 居中显示
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.setGeometry(x, y, window_width, window_height)
        self.setMinimumSize(min_width, min_height)

    def _initialize_ui_managers(self):
        """初始化UI管理器"""
        # 初始化进度管理器
        self.progress_manager = ProgressManager(self.progress_bar, self.status_label)

        # 初始化状态管理器
        self.status_manager = StatusManager(self.status_label)
        self.status_manager.set_ready()

        # 连接错误处理器信号
        self.error_handler.error_occurred.connect(self._on_error_occurred)

    def _on_error_occurred(self, error_type: str, error_message: str):
        """处理错误事件"""
        # 可以在这里添加全局错误处理逻辑
        logger.error(f"全局错误处理: [{error_type}] {error_message}")

    def _initialize_ai_service(self):
        """初始化AI服务"""
        self.status_label.setText("正在初始化AI服务...")
        
        # 使用定时器异步检查服务状态
        self.init_timer = QTimer()
        self.init_timer.timeout.connect(self._check_service_status)
        self.init_timer.start(1000)  # 每秒检查一次
        
        # 尝试初始化服务
        try:
            success = self.ai_client.initialize_service()
            if success:
                self.status_label.setText("AI服务初始化成功")
                self._load_ai_options()
            else:
                self.status_label.setText("AI服务初始化失败，请检查后端服务")
        except Exception as e:
            self.status_label.setText(f"连接AI服务失败: {str(e)}")

    def _check_service_status(self):
        """检查服务状态"""
        try:
            status = self.ai_client.get_service_status()
            if status.get('service_status') == 'running':
                self.status_label.setText("AI服务运行中")
                self.ai_generate_btn.setEnabled(True)
                self.init_timer.stop()
            else:
                self.status_label.setText("AI服务未就绪...")
                self.ai_generate_btn.setEnabled(False)
        except:
            self.status_label.setText("无法连接到AI服务")
            self.ai_generate_btn.setEnabled(False)

    def _load_ai_options(self):
        """加载AI服务选项"""
        try:
            # 加载采样器列表
            scheduler_info = self.ai_client.get_available_schedulers()
            schedulers = scheduler_info.get('schedulers', [])
            if schedulers and hasattr(self, 'sampler_combo'):
                self.sampler_combo.clear()
                self.sampler_combo.addItems(schedulers)

            # 加载可用模型列表
            self._load_available_models()

        except Exception as e:
            print(f"加载AI选项失败: {str(e)}")

    def _load_available_models(self):
        """加载可用的AI模型列表"""
        try:
            # 获取传统AI模型
            models_info = self.ai_client.get_available_models()
            self.available_models = models_info.get('models', {})
            self.current_model_info = models_info.get('current_model', {})

            # 获取SD权重模型
            try:
                sd_models_result = self.sd_model_service.get_available_models()
                if sd_models_result.get('success'):
                    sd_models = sd_models_result.get('data', {}).get('models', {})
                    # 合并SD模型到可用模型列表
                    for model_key, model_info in sd_models.items():
                        if model_info.get('model_type') == 'safetensors':
                            # 标记为SD权重模型
                            model_info['is_sd_weight'] = True
                            self.available_models[model_key] = model_info
            except Exception as e:
                logger.warning(f"加载SD权重模型失败: {str(e)}")

            # 填充模型下拉框
            if hasattr(self, 'model_combo'):
                self.model_combo.clear()

                for model_key, model_config in self.available_models.items():
                    display_name = f"{model_key} - {model_config.get('description', '')}"
                    self.model_combo.addItem(display_name, model_key)

                # 设置当前选中的模型
                current_model_key = self.current_model_info.get('model_key')
                if current_model_key:
                    for i in range(self.model_combo.count()):
                        if self.model_combo.itemData(i) == current_model_key:
                            self.model_combo.setCurrentIndex(i)
                            break

                # 启用切换按钮
                self.switch_model_btn.setEnabled(True)

                # 更新模型状态
                self._update_model_status()

                # 连接模型选择变化事件
                self.model_combo.currentTextChanged.connect(self._on_model_selection_changed)

                # 初始化缓存模型列表
                self._update_cached_models()

        except Exception as e:
            print(f"加载模型列表失败: {str(e)}")
            if hasattr(self, 'model_status_label'):
                self.model_status_label.setText("加载失败")

    def _update_model_status(self):
        """更新模型状态显示"""
        if self.current_model_info.get('loaded', False):
            model_key = self.current_model_info.get('model_key', '未知')

            # 检查是否是SD权重模型
            model_config = self.available_models.get(model_key, {})
            is_sd_weight = model_config.get('is_sd_weight', False)

            if is_sd_weight:
                self.model_status_label.setText(f"已加载: {model_key} (SD权重)")
                StyleUtils.apply_model_status_style(self.model_status_label, 'sd_weight')
            else:
                self.model_status_label.setText(f"已加载: {model_key}")
                StyleUtils.apply_model_status_style(self.model_status_label, 'loaded')
        else:
            self.model_status_label.setText("未加载")
            StyleUtils.apply_model_status_style(self.model_status_label, 'unloaded')

    def _on_model_selection_changed(self):
        """模型选择变化时的处理"""
        selected_model_key = self.model_combo.currentData()
        current_model_key = self.current_model_info.get('model_key')

        # 如果选择的模型与当前模型不同，启用切换按钮
        if selected_model_key and selected_model_key != current_model_key:
            self.switch_model_btn.setEnabled(True)
            self.switch_model_btn.setText("切换模型")

            # 触发智能预加载
            self._schedule_smart_preload(selected_model_key)
        else:
            self.switch_model_btn.setEnabled(False)
            self.switch_model_btn.setText("当前模型")

            # 取消预加载
            self._cancel_smart_preload()

    def _switch_model(self):
        """切换AI模型"""
        selected_model_key = self.model_combo.currentData()
        if not selected_model_key:
            QMessageBox.warning(self, "警告", "请先选择要切换的模型")
            return

        current_model_key = self.current_model_info.get('model_key')
        if selected_model_key == current_model_key:
            QMessageBox.information(self, "提示", "已经是当前模型")
            return

        # 获取模型信息用于确认对话框
        model_config = self.available_models.get(selected_model_key, {})
        model_description = model_config.get('description', '未知模型')
        recommended_for = model_config.get('recommended_for', [])

        # 显示确认对话框
        confirm_msg = f"确定要切换到模型: {selected_model_key}？\n\n"
        confirm_msg += f"描述: {model_description}\n"
        if recommended_for:
            confirm_msg += f"适用场景: {', '.join(recommended_for)}\n"
        confirm_msg += "\n注意: 模型切换可能需要一些时间，期间无法生成图像。"

        reply = QMessageBox.question(
            self, "确认模型切换", confirm_msg,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 禁用相关控件
        self.switch_model_btn.setEnabled(False)
        self.switch_model_btn.setText("切换中...")
        self.ai_generate_btn.setEnabled(False)
        self.model_combo.setEnabled(False)

        # 显示进度
        self.status_label.setText(f"正在切换到模型: {selected_model_key}...")

        # 检查是否是SD权重模型
        model_config = self.available_models.get(selected_model_key, {})
        is_sd_weight = model_config.get('is_sd_weight', False)

        if is_sd_weight:
            # 使用SD权重加载器
            self.model_switch_worker = SDModelSwitchWorker(self.sd_model_service, selected_model_key)
        else:
            # 使用传统AI模型切换器
            self.model_switch_worker = ModelSwitchWorker(self.ai_client, selected_model_key)

        self.model_switch_worker.progress.connect(self._on_model_switch_progress)
        self.model_switch_worker.finished.connect(self._on_model_switch_finished)
        self.model_switch_worker.error.connect(self._on_model_switch_error)
        self.model_switch_worker.start()

    def _on_model_switch_progress(self, message):
        """模型切换进度更新"""
        self.status_label.setText(message)

    def _on_model_switch_finished(self, result):
        """模型切换完成"""
        # 重新启用控件
        self.model_combo.setEnabled(True)
        self.ai_generate_btn.setEnabled(True)

        if result.get('success'):
            # 更新当前模型信息
            self.current_model_info = result.get('data', {}).get('current_model', {})

            # 更新状态显示
            model_key = self.current_model_info.get('model_key', '未知')
            self.status_label.setText(f"成功切换到模型: {model_key}")
            self._update_model_status()

            # 重置切换按钮
            self.switch_model_btn.setText("当前模型")
            self.switch_model_btn.setEnabled(False)

            # 根据新模型自动调整生成参数
            self._adjust_generation_params_for_model(model_key)

            QMessageBox.information(self, "切换成功", f"成功切换到模型: {model_key}")
        else:
            error_msg = result.get('message', '模型切换失败')
            self.status_label.setText(f"模型切换失败: {error_msg}")
            self.switch_model_btn.setText("切换模型")
            self.switch_model_btn.setEnabled(True)

            # 检查是否是safetensors组件缺失错误
            if "文本编码器组件" in error_msg:
                # 显示详细的错误信息和建议
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("模型切换失败")
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setText("safetensors文件不完整")
                msg_box.setDetailedText(f"模型切换失败:\n\n{error_msg}")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()
            else:
                QMessageBox.critical(self, "切换失败", f"模型切换失败:\n{error_msg}")

    def _on_model_switch_error(self, error_message):
        """模型切换错误"""
        # 重新启用控件
        self.model_combo.setEnabled(True)
        self.ai_generate_btn.setEnabled(True)
        self.switch_model_btn.setText("切换模型")
        self.switch_model_btn.setEnabled(True)

        self.status_manager.set_status(f"模型切换失败: {error_message}")

        # 检查是否是模型文件缺失的错误
        if any(keyword in error_message.lower() for keyword in [
            "safetensors", "not found", "缺失", "下载", "文件", "模型文件"
        ]):
            # 显示带有下载管理选项的错误对话框
            reply = QMessageBox.question(
                self, "模型加载失败",
                f"模型切换失败:\n{error_message}\n\n"
                "这可能是因为模型文件缺失或配置不完整。\n"
                "是否打开下载管理对话框来解决此问题？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                self._show_model_download_dialog()
        else:
            self.error_handler.handle_error("model", error_message, self)

    def _show_model_download_dialog(self):
        """显示模型下载管理对话框"""
        selected_model_key = self.model_combo.currentData()
        if not selected_model_key:
            QMessageBox.warning(self, "警告", "请先选择一个模型")
            return

        try:
            from widgets.model_download_dialog import ModelDownloadDialog
            
            dialog = ModelDownloadDialog(
                parent=self,
                ai_client=self.ai_client,
                model_key=selected_model_key
            )
            dialog.exec()
            
        except Exception as e:
            logger.error(f"显示模型下载对话框失败: {str(e)}")
            QMessageBox.critical(
                self, "错误", 
                f"无法打开模型下载管理对话框:\n{str(e)}"
            )

    def _show_custom_model_dialog(self):
        """显示自定义模型管理对话框"""
        try:
            from widgets.custom_model_dialog import CustomModelDialog

            dialog = CustomModelDialog(self)
            dialog.exec()

            # 对话框关闭后刷新可用模型列表
            self._load_available_models()

        except Exception as e:
            logger.error(f"显示自定义模型对话框失败: {str(e)}")
            QMessageBox.critical(
                self, "错误",
                f"无法打开自定义模型管理对话框:\n{str(e)}"
            )

    def _show_sd_weights_dialog(self):
        """显示SD权重管理对话框"""
        try:
            from widgets.sd_weights_dialog import SDWeightsDialog

            dialog = SDWeightsDialog(self.sd_model_service, self)
            dialog.exec()

            # 对话框关闭后刷新可用模型列表
            self._load_available_models()

        except Exception as e:
            logger.error(f"显示SD权重对话框失败: {str(e)}")
            QMessageBox.critical(
                self, "错误",
                f"无法打开SD权重管理对话框:\n{str(e)}"
            )

    def _adjust_generation_params_for_model(self, model_key):
        """根据模型自动调整生成参数"""
        model_config = self.available_models.get(model_key, {})

        # 获取推荐的CFG范围
        cfg_range = model_config.get('cfg_scale_range', (7.0, 12.0))
        recommended_cfg = sum(cfg_range) / 2
        self.cfg_spin.setValue(recommended_cfg)

        # 获取推荐的步数范围
        steps_range = model_config.get('steps_range', (20, 50))
        recommended_steps = int(sum(steps_range) / 2)
        self.steps_spin.setValue(recommended_steps)

        # 显示参数调整提示
        self.status_label.setText(
            f"已根据模型 {model_key} 自动调整参数: CFG={recommended_cfg}, 步数={recommended_steps}"
        )

    def _schedule_smart_preload(self, model_key: str):
        """安排智能预加载"""
        # 如果正在生成图像，不进行预加载
        if (self.generation_worker and self.generation_worker.isRunning()) or \
           (self.batch_worker and self.batch_worker.isRunning()):
            return

        # 如果模型已经缓存，不需要预加载
        if model_key in self.cached_models:
            self._update_model_status_for_cached(model_key)
            return

        # 取消之前的预加载计划
        self.preload_timer.stop()

        # 设置新的预加载目标
        self.pending_preload_model = model_key

        # 启动延迟定时器
        self.preload_timer.start(self.preload_delay)

        # 更新状态提示
        self.model_status_label.setText(f"将预加载: {model_key}")
        StyleUtils.apply_model_status_style(self.model_status_label, 'pending')

    def _cancel_smart_preload(self):
        """取消智能预加载"""
        self.preload_timer.stop()
        self.pending_preload_model = None

        # 恢复状态显示
        self._update_model_status()

    def _trigger_smart_preload(self):
        """触发智能预加载"""
        if not self.pending_preload_model:
            return

        model_key = self.pending_preload_model

        # 检查是否仍然需要预加载
        if model_key in self.cached_models:
            self._update_model_status_for_cached(model_key)
            return

        # 检查是否正在进行其他操作
        if (self.generation_worker and self.generation_worker.isRunning()) or \
           (self.batch_worker and self.batch_worker.isRunning()) or \
           (self.model_switch_worker and self.model_switch_worker.isRunning()) or \
           self.is_preloading:
            # 延迟重试
            self.preload_timer.start(self.preload_delay)
            return

        # 开始预加载
        self._start_model_preload(model_key)

    def _start_model_preload(self, model_key: str):
        """开始模型预加载"""
        self.is_preloading = True
        self.pending_preload_model = None

        # 更新状态显示
        self.model_status_label.setText(f"正在预加载: {model_key}")
        StyleUtils.apply_model_status_style(self.model_status_label, 'preloading')

        # 启动预加载工作线程
        self.model_preload_worker = ModelPreloadWorker(self.ai_client, model_key)
        self.model_preload_worker.progress.connect(self._on_model_preload_progress)
        self.model_preload_worker.finished.connect(self._on_model_preload_finished)
        self.model_preload_worker.error.connect(self._on_model_preload_error)
        self.model_preload_worker.start()

    def _on_model_preload_progress(self, message):
        """模型预加载进度更新"""
        # 只在状态栏显示，不打断用户操作
        pass

    def _on_model_preload_finished(self, result):
        """模型预加载完成"""
        self.is_preloading = False

        if result.get('success'):
            # 更新缓存列表
            self._update_cached_models()

            # 获取预加载的模型
            preloaded_model = result.get('data', {}).get('preloaded_model')
            if preloaded_model:
                # 更新状态显示
                self.model_status_label.setText(f"已预加载: {preloaded_model}")
                StyleUtils.apply_model_status_style(self.model_status_label, 'cached')

                # 如果这是当前选中的模型，更新切换按钮
                selected_model_key = self.model_combo.currentData()
                if selected_model_key == preloaded_model:
                    self.switch_model_btn.setText("快速切换")
        else:
            # 预加载失败，恢复状态
            self._update_model_status()

    def _on_model_preload_error(self, error_message):
        """模型预加载错误"""
        self.is_preloading = False

        # 恢复状态显示
        self._update_model_status()

        # 记录错误但不打断用户操作
        print(f"模型预加载失败: {error_message}")

    def _update_cached_models(self):
        """更新缓存模型列表"""
        try:
            cached_info = self.ai_client.get_cached_models()
            self.cached_models = cached_info.get('cached_models', [])
        except Exception as e:
            print(f"获取缓存模型列表失败: {str(e)}")

    def _update_model_status_for_cached(self, model_key: str):
        """为缓存模型更新状态显示"""
        current_model_key = self.current_model_info.get('model_key')

        if model_key == current_model_key:
            self._update_model_status()
        else:
            self.model_status_label.setText(f"已缓存: {model_key}")
            StyleUtils.apply_model_status_style(self.model_status_label, 'cached')

            # 更新切换按钮
            selected_model_key = self.model_combo.currentData()
            if selected_model_key == model_key:
                self.switch_model_btn.setText("快速切换")

    def _create_main_widget(self):
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        main_layout = QHBoxLayout(main_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 使用分割器来分隔左右面板
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：功能区
        left_panel = self._create_left_panel()
        
        # 右侧：图像预览区
        right_panel = self._create_right_panel()
        
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # 使用比例而不是固定像素值，适应不同屏幕尺寸
        total_width = self.width()
        left_width = int(total_width * 0.6)  # 左侧占60%
        right_width = int(total_width * 0.4)  # 右侧占40%
        splitter.setSizes([left_width, right_width])
        
        main_layout.addWidget(splitter)

    def _create_left_panel(self):
        """创建功能面板"""
        left_panel = QFrame()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)

        tab_widget = QTabWidget()

        traditional_tab = self._create_traditional_generation_tab()
        ai_tab = self._create_ai_generation_tab()
        data_management_tab = self._create_data_management_tab()

        tab_widget.addTab(traditional_tab, "传统生成")
        tab_widget.addTab(ai_tab, "AI 生成")
        tab_widget.addTab(data_management_tab, "数据管理")
        
        # 添加图标
        style = self.style()
        tab_widget.setTabIcon(0, style.standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        tab_widget.setTabIcon(1, style.standardIcon(QStyle.StandardPixmap.SP_MediaPlay))
        tab_widget.setTabIcon(2, style.standardIcon(QStyle.StandardPixmap.SP_DirIcon))

        left_layout.addWidget(tab_widget)
        return left_panel

    def _create_right_panel(self):
        """创建右侧图像预览面板"""
        right_panel = QFrame()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(8, 8, 8, 8)
        right_layout.setSpacing(12)
        
        # 图像预览区
        preview_group = QGroupBox("生成图像预览")
        preview_layout = QVBoxLayout(preview_group)
        preview_layout.setSpacing(8)
        preview_layout.setContentsMargins(10, 15, 10, 10)
        
        # 图片显示区域
        self.preview_label = QLabel("生成的图像将在此处显示")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 使用响应式工具类设置预览区域大小
        preview_size = ResponsiveUtils.get_responsive_preview_size()
        self.preview_label.setMinimumSize(preview_size)

        self.preview_label.setScaledContents(False)
        self.preview_label.setStyleSheet(StyleUtils.get_preview_placeholder_style())
        preview_layout.addWidget(self.preview_label)
        
        # 图片导航控制
        nav_layout = QHBoxLayout()
        self.prev_btn = ButtonStyleManager.create_button("◀ 上一张", "secondary")
        self.next_btn = ButtonStyleManager.create_button("下一张 ▶", "secondary")
        self.image_info_label = QLabel("0/0")

        self.prev_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.prev_btn.clicked.connect(self._show_previous_image)
        self.next_btn.clicked.connect(self._show_next_image)

        nav_layout.addWidget(self.prev_btn)
        nav_layout.addStretch()
        nav_layout.addWidget(self.image_info_label)
        nav_layout.addStretch()
        nav_layout.addWidget(self.next_btn)

        # 添加测试按钮
        test_btn = ButtonStyleManager.create_button("测试显示", "secondary")
        test_btn.clicked.connect(self._test_image_display)
        nav_layout.addWidget(test_btn)
        
        preview_layout.addLayout(nav_layout)
        
        # 生成状态信息
        status_group = QGroupBox("生成状态")
        status_layout = QVBoxLayout(status_group)
        status_layout.setSpacing(6)
        status_layout.setContentsMargins(10, 15, 10, 10)
        
        self.status_label = QLabel("等待开始生成...")
        StyleUtils.apply_status_style(self.status_label, 'default')

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        self.progress_label = QLabel("进度: 0/0")
        StyleUtils.apply_progress_label_style(self.progress_label)

        # 批量生成进度
        self.batch_progress_bar = QProgressBar()
        self.batch_progress_bar.setVisible(False)
        self.batch_progress_label = QLabel("批次进度: 0/0")
        StyleUtils.apply_progress_label_style(self.batch_progress_label)
        
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.progress_label)
        status_layout.addWidget(self.batch_progress_bar)
        status_layout.addWidget(self.batch_progress_label)
        
        right_layout.addWidget(preview_group, 3)
        right_layout.addWidget(status_group, 1)
        
        return right_panel

    def _create_radio_button_group(self, title, options):
        """创建单选按钮组"""
        group_box = QGroupBox(title)
        layout = QHBoxLayout(group_box)
        radio_buttons = []
        for option in options:
            radio = QRadioButton(option)
            layout.addWidget(radio)
            radio_buttons.append(radio)
        
        if radio_buttons:
            radio_buttons[0].setChecked(True)

        layout.addStretch()
        return group_box, radio_buttons

    def _create_traditional_generation_tab(self):
        """创建简化的传统生成选项卡"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        layout.setContentsMargins(10, 10, 10, 10)

        # 基础选择组
        selection_group = QGroupBox("目标与场景选择")
        selection_layout = QVBoxLayout(selection_group)
        selection_layout.setSpacing(10)
        selection_layout.setContentsMargins(15, 20, 15, 15)

        targets = ["坦克", "战机", "舰艇"]
        target_group, self.trad_target_radios = self._create_radio_button_group("军事目标", targets)
        selection_layout.addWidget(target_group)

        weathers = ["晴天", "雨天", "雪天", "大雾", "夜间"]
        weather_group, self.trad_weather_radios = self._create_radio_button_group("天气条件", weathers)
        selection_layout.addWidget(weather_group)

        scenes = ["城市", "岛屿", "乡村"]
        scene_group, self.trad_scene_radios = self._create_radio_button_group("地形场景", scenes)
        selection_layout.addWidget(scene_group)

        # 简化的合成配置组
        composition_group = QGroupBox("合成配置")
        composition_layout = QFormLayout(composition_group)
        composition_layout.setSpacing(10)
        composition_layout.setContentsMargins(15, 20, 15, 15)

        # 目标大小
        self.target_scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.target_scale_slider.setRange(10, 80)
        self.target_scale_slider.setValue(30)
        self.target_scale_label = QLabel("30%")
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(self.target_scale_slider)
        scale_layout.addWidget(self.target_scale_label)
        composition_layout.addRow("目标大小:", scale_layout)

        # 透明度
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(50, 100)
        self.opacity_slider.setValue(100)
        self.opacity_label = QLabel("100%")
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        composition_layout.addRow("透明度:", opacity_layout)

        # 位置选择
        self.random_position_cb = QCheckBox("随机位置")
        self.random_position_cb.setChecked(True)
        composition_layout.addRow("", self.random_position_cb)

        # 添加阴影
        self.add_shadow_cb = QCheckBox("添加阴影")
        self.add_shadow_cb.setChecked(False)
        composition_layout.addRow("", self.add_shadow_cb)

        # 启用抠图
        self.enable_matting_cb = QCheckBox("启用智能抠图")
        self.enable_matting_cb.setChecked(True)
        composition_layout.addRow("", self.enable_matting_cb)

        # 抠图方法选择
        self.matting_method_combo = QComboBox()
        self.matting_method_combo.addItems(["自动选择", "深度学习(rembg)", "传统算法(GrabCut)"])
        self.matting_method_combo.setCurrentText("自动选择")
        composition_layout.addRow("抠图方法:", self.matting_method_combo)

        # 抠图方法选择只在启用抠图时可用
        self.matting_method_combo.setEnabled(self.enable_matting_cb.isChecked())
        self.enable_matting_cb.toggled.connect(self.matting_method_combo.setEnabled)

        # 生成配置组
        generation_group = QGroupBox("生成配置")
        generation_layout = QFormLayout(generation_group)
        generation_layout.setSpacing(10)
        generation_layout.setContentsMargins(15, 20, 15, 15)

        # 生成数量
        self.trad_num_images_spin = QSpinBox()
        self.trad_num_images_spin.setRange(1, 20)
        self.trad_num_images_spin.setValue(1)
        generation_layout.addRow("生成数量:", self.trad_num_images_spin)

        # 添加说明文本
        size_info_label = QLabel("图像尺寸：自动使用背景图片原始尺寸")
        StyleUtils.apply_info_text_style(size_info_label)
        generation_layout.addRow("", size_info_label)

        # 连接信号
        self.target_scale_slider.valueChanged.connect(
            lambda v: self.target_scale_label.setText(f"{v}%")
        )
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )

        # 生成按钮
        self.trad_generate_btn = ButtonStyleManager.create_button("开始合成", "primary")
        self.trad_generate_btn.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogOkButton))
        self.trad_generate_btn.clicked.connect(self._generate_traditional_images)

        # 添加到布局
        layout.addWidget(selection_group)
        layout.addWidget(composition_group)
        layout.addWidget(generation_group)
        layout.addStretch()
        layout.addWidget(self.trad_generate_btn)

        scroll_area.setWidget(tab)
        return scroll_area

    def _create_ai_generation_tab(self):
        """创建AI生成标签页"""
        # 创建滚动区域以适应小屏幕
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)  # 减少间距以节省空间
        layout.setContentsMargins(6, 6, 6, 6)  # 减少边距

        # 目标选择组
        selection_group = QGroupBox("目标与场景选择")
        selection_layout = QVBoxLayout(selection_group)
        selection_layout.setSpacing(6)  # 减少间距
        selection_layout.setContentsMargins(8, 10, 8, 8)  # 减少边距

        # 军事目标选择
        target_group_widget, self.ai_target_radios = self._create_radio_button_group("军事目标", ["坦克", "战机", "舰艇"])
        selection_layout.addWidget(target_group_widget)

        # 天气条件选择
        weather_group_widget, self.ai_weather_radios = self._create_radio_button_group("天气条件", ["雨天", "雪天", "大雾", "夜间"])
        selection_layout.addWidget(weather_group_widget)

        # 场景环境选择
        scene_group_widget, self.ai_scene_radios = self._create_radio_button_group("场景环境", ["城市", "岛屿", "乡村"])
        selection_layout.addWidget(scene_group_widget)

        # 为所有单选按钮添加事件监听
        for radio in self.ai_target_radios + self.ai_weather_radios + self.ai_scene_radios:
            radio.toggled.connect(self._update_ai_prompt)

        # 提示词组
        prompt_group = QGroupBox("提示词预览")
        prompt_layout = QVBoxLayout(prompt_group)
        prompt_layout.setSpacing(6)  # 减少间距
        prompt_layout.setContentsMargins(8, 10, 8, 8)  # 减少边距

        # 添加说明标签和重置按钮
        info_layout = QHBoxLayout()
        prompt_info_label = QLabel("提示词将根据上方选择自动生成，您也可以手动编辑：")
        StyleUtils.apply_hint_text_style(prompt_info_label)
        info_layout.addWidget(prompt_info_label)

        # 添加重置按钮
        reset_prompt_btn = QPushButton("重置")
        reset_prompt_btn.setMaximumWidth(50)
        reset_prompt_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        reset_prompt_btn.clicked.connect(self._reset_prompt)
        reset_prompt_btn.setToolTip("清空手动编辑的内容，恢复自动生成")
        info_layout.addWidget(reset_prompt_btn)
        info_layout.addStretch()

        prompt_layout.addLayout(info_layout)

        self.ai_prompt_text = QTextEdit()
        # 在小屏幕上使用更紧凑的高度
        self.ai_prompt_text.setMaximumHeight(80)
        self.ai_prompt_text.setMinimumHeight(60)
        self.ai_prompt_text.setPlaceholderText("请先选择军事目标、天气条件和场景环境...")

        # 监听文本变化，当用户手动编辑时更新状态
        self.ai_prompt_text.textChanged.connect(self._on_prompt_text_changed)

        prompt_layout.addWidget(self.ai_prompt_text)

        # 生成配置组
        config_group = QGroupBox("生成配置")
        config_layout = QFormLayout(config_group)
        config_layout.setSpacing(4)  # 减少间距
        config_layout.setContentsMargins(8, 10, 8, 8)  # 减少边距

        # 生成模式选择
        mode_layout = QHBoxLayout()
        self.single_mode_radio = QRadioButton("单张生成")
        self.batch_mode_radio = QRadioButton("批量生成")
        self.single_mode_radio.setChecked(True)
        
        self.single_mode_radio.toggled.connect(self._on_generation_mode_changed)
        self.batch_mode_radio.toggled.connect(self._on_generation_mode_changed)
        
        mode_layout.addWidget(self.single_mode_radio)
        mode_layout.addWidget(self.batch_mode_radio)
        mode_layout.addStretch()
        config_layout.addRow("生成模式:", mode_layout)

        # 生成数量（批量模式）
        self.batch_count_spin = QSpinBox()
        self.batch_count_spin.setRange(1, 100)
        self.batch_count_spin.setValue(5)
        self.batch_count_spin.setEnabled(False)
        config_layout.addRow("生成数量:", self.batch_count_spin)

        # 每张图片数量
        self.num_images_spin = QSpinBox()
        self.num_images_spin.setRange(1, 10)
        self.num_images_spin.setValue(1)
        config_layout.addRow("每组图片数:", self.num_images_spin)

        # 采样步数
        self.steps_spin = QSpinBox()
        self.steps_spin.setRange(10, 100)
        self.steps_spin.setValue(30)
        config_layout.addRow("采样步数:", self.steps_spin)

        # CFG引导强度
        self.cfg_spin = QDoubleSpinBox()
        self.cfg_spin.setRange(1.0, 20.0)
        self.cfg_spin.setValue(7.5)
        self.cfg_spin.setSingleStep(0.5)
        config_layout.addRow("CFG强度:", self.cfg_spin)

        # 随机种子
        seed_layout = QHBoxLayout()
        self.seed_spin = QSpinBox()
        self.seed_spin.setRange(-1, 2147483647)
        self.seed_spin.setValue(-1)
        self.seed_spin.setSpecialValueText("随机")
        
        random_seed_btn = ButtonStyleManager.create_button("随机", "secondary", min_width=50)
        random_seed_btn.clicked.connect(lambda: self.seed_spin.setValue(-1))
        
        seed_layout.addWidget(self.seed_spin)
        seed_layout.addWidget(random_seed_btn)
        config_layout.addRow("种子:", seed_layout)

        # 采样器
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems([
            "DPM++ 2M Karras", "Euler a", "Euler", "LMS",
            "Heun", "DPM2", "DPM2 a", "DPM++ 2S a", "DDIM"
        ])
        config_layout.addRow("采样器:", self.sampler_combo)

        # AI模型选择
        model_selection_layout = QHBoxLayout()
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(200)
        self.model_combo.setToolTip("选择用于生成的AI模型")

        self.switch_model_btn = ButtonStyleManager.create_button("切换模型", "secondary", min_width=70)
        self.switch_model_btn.clicked.connect(self._switch_model)
        self.switch_model_btn.setEnabled(False)

        self.model_status_label = QLabel("未加载")
        StyleUtils.apply_model_status_style(self.model_status_label, 'unloaded')

        model_selection_layout.addWidget(self.model_combo)
        model_selection_layout.addWidget(self.switch_model_btn)
        model_selection_layout.addWidget(self.model_status_label)
        model_selection_layout.addStretch()
        config_layout.addRow("AI模型:", model_selection_layout)

        # 模型管理按钮
        model_management_layout = QHBoxLayout()
        self.model_download_btn = ButtonStyleManager.create_button("下载管理", "secondary", min_width=70)
        self.model_download_btn.clicked.connect(self._show_model_download_dialog)
        self.model_download_btn.setToolTip("管理模型下载和离线配置")

        self.custom_models_btn = ButtonStyleManager.create_button("自定义模型", "secondary", min_width=70)
        self.custom_models_btn.clicked.connect(self._show_custom_model_dialog)
        self.custom_models_btn.setToolTip("管理自定义训练的模型")

        self.sd_weights_btn = ButtonStyleManager.create_button("SD权重", "secondary", min_width=70)
        self.sd_weights_btn.clicked.connect(self._show_sd_weights_dialog)
        self.sd_weights_btn.setToolTip("管理Stable Diffusion权重文件(.safetensors)")

        model_management_layout.addWidget(self.model_download_btn)
        model_management_layout.addWidget(self.custom_models_btn)
        model_management_layout.addWidget(self.sd_weights_btn)
        model_management_layout.addStretch()
        config_layout.addRow("", model_management_layout)

        # GSA检测选项
        gsa_layout = QHBoxLayout()
        self.enable_gsa_cb = QCheckBox("启用GSA智能检测")
        self.enable_gsa_cb.setToolTip("生成图片后自动进行Grounded-Segment-Anything目标检测和边界框标注")
        self.enable_gsa_cb.setChecked(True)  # 默认启用

        # GSA配置按钮
        self.gsa_config_btn = ButtonStyleManager.create_button("检测配置", "secondary", min_width=70)
        self.gsa_config_btn.clicked.connect(self._show_gsa_config)
        self.gsa_config_btn.setEnabled(True)

        gsa_layout.addWidget(self.enable_gsa_cb)
        gsa_layout.addWidget(self.gsa_config_btn)
        gsa_layout.addStretch()
        config_layout.addRow("智能检测:", gsa_layout)

        # GSA检测参数（默认值）
        self.gsa_confidence = 0.3
        self.gsa_nms_threshold = 0.5
        self.gsa_save_annotated = True
        self.gsa_save_original = True

        # 批量生成选项
        batch_options_group = QGroupBox("批量生成选项")
        batch_options_layout = QVBoxLayout(batch_options_group)
        batch_options_layout.setSpacing(6)
        batch_options_layout.setContentsMargins(10, 15, 10, 10)
        
        self.random_target_cb = QCheckBox("随机军事目标")
        self.random_weather_cb = QCheckBox("随机天气条件")
        self.random_scene_cb = QCheckBox("随机场景环境")
        self.random_seed_cb = QCheckBox("每次使用随机种子")
        self.random_seed_cb.setChecked(True)
        
        batch_options_layout.addWidget(self.random_target_cb)
        batch_options_layout.addWidget(self.random_weather_cb)
        batch_options_layout.addWidget(self.random_scene_cb)
        batch_options_layout.addWidget(self.random_seed_cb)
        
        batch_options_group.setEnabled(False)
        self.batch_options_group = batch_options_group

        # 生成按钮
        self.ai_generate_btn = ButtonStyleManager.create_button("生成图像", "primary")
        self.ai_generate_btn.clicked.connect(self._generate_ai_images)
        self.ai_generate_btn.setEnabled(False)  # 初始禁用，等待服务就绪

        layout.addWidget(selection_group)
        layout.addWidget(prompt_group)
        layout.addWidget(config_group)
        layout.addWidget(batch_options_group)
        layout.addStretch()
        layout.addWidget(self.ai_generate_btn)

        # 初始化提示词
        self._update_ai_prompt()

        # 将tab放入滚动区域
        scroll_area.setWidget(tab)
        return scroll_area

    def _create_data_management_tab(self):
        """创建数据管理标签页"""
        try:
            data_management_widget = DatasetManagementWidget()
            return data_management_widget
        except Exception as e:
            # 如果创建失败，返回一个简单的错误页面
            error_tab = QWidget()
            error_layout = QVBoxLayout(error_tab)
            error_label = QLabel(f"数据管理页面加载失败:\n{str(e)}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            StyleUtils.apply_error_text_style(error_label)
            error_layout.addWidget(error_label)
            return error_tab

    def _on_generation_mode_changed(self):
        """生成模式改变时的处理"""
        is_batch_mode = self.batch_mode_radio.isChecked()
        self.batch_count_spin.setEnabled(is_batch_mode)
        self.batch_options_group.setEnabled(is_batch_mode)
        
        if is_batch_mode:
            self.ai_generate_btn.setText("批量生成图像")
        else:
            self.ai_generate_btn.setText("生成图像")

    def _show_gsa_config(self):
        """显示GSA配置对话框"""
        from PyQt6.QtWidgets import QDialog, QDialogButtonBox, QDoubleSpinBox

        dialog = QDialog(self)
        dialog.setWindowTitle("GSA检测配置")
        dialog.setModal(True)
        # 使用更小的对话框尺寸
        dialog.resize(320, 220)

        layout = QVBoxLayout(dialog)

        # 配置组
        config_group = QGroupBox("检测参数")
        config_layout = QFormLayout(config_group)

        # 置信度阈值
        confidence_spin = QDoubleSpinBox()
        confidence_spin.setRange(0.1, 1.0)
        confidence_spin.setSingleStep(0.05)
        confidence_spin.setValue(self.gsa_confidence)
        confidence_spin.setToolTip("GSA检测置信度阈值，越高越严格")
        config_layout.addRow("置信度阈值:", confidence_spin)

        # NMS阈值
        nms_spin = QDoubleSpinBox()
        nms_spin.setRange(0.1, 1.0)
        nms_spin.setSingleStep(0.05)
        nms_spin.setValue(self.gsa_nms_threshold)
        nms_spin.setToolTip("非极大值抑制阈值，用于去除重叠检测框")
        config_layout.addRow("NMS阈值:", nms_spin)

        # 保存选项组
        save_group = QGroupBox("保存选项")
        save_layout = QVBoxLayout(save_group)

        save_original_cb = QCheckBox("保存原始图片")
        save_original_cb.setChecked(self.gsa_save_original)
        save_original_cb.setToolTip("保存GSA检测前的原始图片")

        save_annotated_cb = QCheckBox("保存标注图片")
        save_annotated_cb.setChecked(self.gsa_save_annotated)
        save_annotated_cb.setToolTip("保存绘制了边界框的标注图片")
        
        save_layout.addWidget(save_original_cb)
        save_layout.addWidget(save_annotated_cb)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        
        layout.addWidget(config_group)
        layout.addWidget(save_group)
        layout.addStretch()
        layout.addWidget(button_box)
        
        # 显示对话框
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 保存配置
            self.gsa_confidence = confidence_spin.value()
            self.gsa_nms_threshold = nms_spin.value()
            self.gsa_save_original = save_original_cb.isChecked()
            self.gsa_save_annotated = save_annotated_cb.isChecked()

            # 显示配置更新消息
            QMessageBox.information(
                self,
                "配置已更新",
                f"GSA检测配置已更新：\n"
                f"置信度阈值: {self.gsa_confidence}\n"
                f"NMS阈值: {self.gsa_nms_threshold}\n"
                f"保存原始图片: {'是' if self.gsa_save_original else '否'}\n"
                f"保存标注图片: {'是' if self.gsa_save_annotated else '否'}"
            )

    def _get_selected_option(self, radio_buttons):
        """获取选中的单选按钮文本"""
        for rb in radio_buttons:
            if rb.isChecked():
                return rb.text()
        return ""

    def _update_ai_prompt(self):
        """根据选择自动更新提示词"""
        if not hasattr(self, 'ai_target_radios') or not self.ai_target_radios:
            return

        # 避免循环触发
        if self._is_updating_prompt:
            return

        target = self._get_selected_option(self.ai_target_radios)
        weather = self._get_selected_option(self.ai_weather_radios)
        scene = self._get_selected_option(self.ai_scene_radios)

        if target and weather and scene:
            try:
                # 检查当前文本是否为用户手动编辑的内容
                current_text = self.ai_prompt_text.toPlainText().strip()
                is_user_custom = current_text and current_text != self._last_auto_generated_text

                # 构建提示词（不传入custom_prompt，让系统生成标准提示词）
                positive_prompt, negative_prompt = self.ai_client.build_prompt(
                    military_target=target,
                    weather=weather,
                    scene=scene,
                    custom_prompt=""  # 总是生成标准提示词
                )

                if positive_prompt:
                    self._is_updating_prompt = True
                    try:
                        if not is_user_custom:
                            # 如果不是用户自定义内容，直接更新显示的提示词
                            self.ai_prompt_text.setPlainText(positive_prompt)
                            self._last_auto_generated_text = positive_prompt
                        else:
                            # 如果是用户自定义内容，保持用户输入，但在占位符中显示自动生成的提示词
                            self.ai_prompt_text.setPlaceholderText(f"自动生成: {positive_prompt[:100]}...")

                        # 存储当前生成的提示词，供生成时使用
                        self._current_generated_prompt = (positive_prompt, negative_prompt)
                    finally:
                        self._is_updating_prompt = False

            except Exception as e:
                print(f"自动生成提示词失败: {str(e)}")
                self.ai_prompt_text.setPlaceholderText("提示词生成失败，请检查网络连接")
        else:
            # 如果选择不完整，清空提示词并显示提示
            current_text = self.ai_prompt_text.toPlainText().strip()
            if not current_text or current_text == self._last_auto_generated_text:
                self.ai_prompt_text.clear()
                self._last_auto_generated_text = ""
            self.ai_prompt_text.setPlaceholderText("请先选择军事目标、天气条件和场景环境...")
            self._current_generated_prompt = None

    def _on_prompt_text_changed(self):
        """提示词文本变化时的处理"""
        # 如果正在更新提示词，不处理文本变化事件
        if self._is_updating_prompt:
            return

        # 检测用户是否手动编辑了提示词
        current_text = self.ai_prompt_text.toPlainText().strip()

        # 如果文本为空，重置占位符
        if not current_text:
            target = self._get_selected_option(self.ai_target_radios) if hasattr(self, 'ai_target_radios') else ""
            weather = self._get_selected_option(self.ai_weather_radios) if hasattr(self, 'ai_weather_radios') else ""
            scene = self._get_selected_option(self.ai_scene_radios) if hasattr(self, 'ai_scene_radios') else ""

            if target and weather and scene:
                # 如果参数已选择，触发自动更新
                self._update_ai_prompt()
            else:
                self.ai_prompt_text.setPlaceholderText("请先选择军事目标、天气条件和场景环境...")

        # 如果用户清空了文本，重置最后生成的文本记录
        if not current_text:
            self._last_auto_generated_text = ""

    def _reset_prompt(self):
        """重置提示词到自动生成状态"""
        self._is_updating_prompt = True
        try:
            self.ai_prompt_text.clear()
            self._last_auto_generated_text = ""
        finally:
            self._is_updating_prompt = False

        # 触发自动更新
        self._update_ai_prompt()



    def _generate_ai_images(self):
        """生成AI图像"""
        target = self._get_selected_option(self.ai_target_radios)
        weather = self._get_selected_option(self.ai_weather_radios)
        scene = self._get_selected_option(self.ai_scene_radios)

        if not all([target, weather, scene]) and not self._is_batch_mode_with_random():
            QMessageBox.warning(self, "警告", "请先选择军事目标、天气和场景，或在批量模式下启用随机选项")
            return

        # 检查是否需要切换模型
        selected_model_key = self.model_combo.currentData()
        current_model_key = self.current_model_info.get('model_key')

        if selected_model_key and selected_model_key != current_model_key:
            # 如果模型已缓存，快速切换
            if selected_model_key in self.cached_models:
                self._quick_switch_and_generate(selected_model_key)
                return
            else:
                # 需要切换模型，询问用户
                reply = QMessageBox.question(
                    self, "模型切换",
                    f"当前选择的模型 ({selected_model_key}) 与已加载的模型不同。\n是否切换模型后再生成？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    self._switch_and_generate(selected_model_key)
                    return
                else:
                    # 用户选择不切换，使用当前模型生成
                    pass

        # 使用当前模型生成
        if self.batch_mode_radio.isChecked():
            self._start_batch_generation()
        else:
            self._start_single_generation()

    def _quick_switch_and_generate(self, model_key: str):
        """快速切换缓存模型并生成"""
        # 禁用相关控件
        self.switch_model_btn.setEnabled(False)
        self.switch_model_btn.setText("切换中...")
        self.ai_generate_btn.setEnabled(False)
        self.model_combo.setEnabled(False)

        # 显示进度
        self.status_label.setText(f"正在快速切换到模型: {model_key}...")

        # 启动模型切换工作线程
        self.model_switch_worker = ModelSwitchWorker(self.ai_client, model_key)
        self.model_switch_worker.progress.connect(self._on_model_switch_progress)
        self.model_switch_worker.finished.connect(self._on_quick_switch_finished)
        self.model_switch_worker.error.connect(self._on_model_switch_error)
        self.model_switch_worker.start()

    def _switch_and_generate(self, model_key: str):
        """切换模型并生成（非缓存模型）"""
        # 禁用相关控件
        self.switch_model_btn.setEnabled(False)
        self.switch_model_btn.setText("切换中...")
        self.ai_generate_btn.setEnabled(False)
        self.model_combo.setEnabled(False)

        # 显示进度
        self.status_label.setText(f"正在切换到模型: {model_key}...")

        # 启动模型切换工作线程
        self.model_switch_worker = ModelSwitchWorker(self.ai_client, model_key)
        self.model_switch_worker.progress.connect(self._on_model_switch_progress)
        self.model_switch_worker.finished.connect(self._on_switch_and_generate_finished)
        self.model_switch_worker.error.connect(self._on_model_switch_error)
        self.model_switch_worker.start()

    def _on_quick_switch_finished(self, result):
        """快速切换完成后立即生成"""
        # 重新启用控件
        self.model_combo.setEnabled(True)

        if result.get('success'):
            # 更新当前模型信息
            self.current_model_info = result.get('data', {}).get('current_model', {})

            # 更新状态显示
            model_key = self.current_model_info.get('model_key', '未知')
            self.status_label.setText(f"已切换到模型: {model_key}，开始生成...")
            self._update_model_status()

            # 重置切换按钮
            self.switch_model_btn.setText("当前模型")
            self.switch_model_btn.setEnabled(False)

            # 根据新模型自动调整生成参数
            self._adjust_generation_params_for_model(model_key)

            # 立即开始生成
            if self.batch_mode_radio.isChecked():
                self._start_batch_generation()
            else:
                self._start_single_generation()
        else:
            # 切换失败，重新启用按钮
            self.ai_generate_btn.setEnabled(True)
            error_msg = result.get('message', '模型切换失败')
            self.status_label.setText(f"模型切换失败: {error_msg}")
            self.switch_model_btn.setText("切换模型")
            self.switch_model_btn.setEnabled(True)
            QMessageBox.critical(self, "切换失败", f"模型切换失败:\n{error_msg}")

    def _on_switch_and_generate_finished(self, result):
        """切换完成后立即生成（非缓存模型）"""
        # 重新启用控件
        self.model_combo.setEnabled(True)

        if result.get('success'):
            # 更新当前模型信息
            self.current_model_info = result.get('data', {}).get('current_model', {})

            # 更新状态显示
            model_key = self.current_model_info.get('model_key', '未知')
            self.status_label.setText(f"已切换到模型: {model_key}，开始生成...")
            self._update_model_status()

            # 重置切换按钮
            self.switch_model_btn.setText("当前模型")
            self.switch_model_btn.setEnabled(False)

            # 根据新模型自动调整生成参数
            self._adjust_generation_params_for_model(model_key)

            # 更新缓存列表
            self._update_cached_models()

            # 立即开始生成
            if self.batch_mode_radio.isChecked():
                self._start_batch_generation()
            else:
                self._start_single_generation()
        else:
            # 切换失败，重新启用按钮
            self.ai_generate_btn.setEnabled(True)
            error_msg = result.get('message', '模型切换失败')
            self.status_label.setText(f"模型切换失败: {error_msg}")
            self.switch_model_btn.setText("切换模型")
            self.switch_model_btn.setEnabled(True)
            QMessageBox.critical(self, "切换失败", f"模型切换失败:\n{error_msg}")

    def _is_batch_mode_with_random(self):
        """检查是否为批量模式且启用了随机选项"""
        if not self.batch_mode_radio.isChecked():
            return False
        return (self.random_target_cb.isChecked() or 
                self.random_weather_cb.isChecked() or 
                self.random_scene_cb.isChecked())

    def _start_single_generation(self):
        """开始单张生成"""
        # 检查是否有正在运行的生成任务
        if self.generation_worker and self.generation_worker.isRunning():
            print("警告：检测到正在运行的生成任务，跳过本次请求")
            return

        target = self._get_selected_option(self.ai_target_radios)
        weather = self._get_selected_option(self.ai_weather_radios)
        scene = self._get_selected_option(self.ai_scene_radios)

        generation_params = {
            'military_target': target,
            'weather': weather,
            'scene': scene,
            'custom_prompt': self.ai_prompt_text.toPlainText().strip(),
            'num_images': self.num_images_spin.value(),
            'steps': self.steps_spin.value(),
            'cfg_scale': self.cfg_spin.value(),
            'seed': self.seed_spin.value(),
            'width': 512,
            'height': 512,
            'scheduler_name': self.sampler_combo.currentText(),
            'save_images': True,
            'generate_annotations': True,
            # GSA检测参数
            'enable_gsa_detection': self.enable_gsa_cb.isChecked(),
            'gsa_confidence': self.gsa_confidence,
            'gsa_nms_threshold': self.gsa_nms_threshold,
            'gsa_save_original': self.gsa_save_original,
            'gsa_save_annotated': self.gsa_save_annotated,
            # 新增优化参数
            'gsa_enable_multi_scale': True,
            'gsa_enable_enhancement': True,
            'gsa_preset': 'enhanced_detection'
        }

        # 添加当前模型信息到生成参数中（用于日志记录）
        current_model_key = self.current_model_info.get('model_key')
        if current_model_key:
            generation_params['model_info'] = {
                'model_key': current_model_key,
                'model_description': self.available_models.get(current_model_key, {}).get('description', ''),
                'target_size_bias': self.available_models.get(current_model_key, {}).get('target_size_bias', '')
            }
        
        # 禁用生成按钮和模型切换
        self.ai_generate_btn.setEnabled(False)
        self.ai_generate_btn.setText("生成中...")
        self.switch_model_btn.setEnabled(False)
        self.model_combo.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 启动生成工作线程
        self.generation_worker = AIGenerationWorker(self.ai_client, generation_params)
        self.generation_worker.progress.connect(self._on_generation_progress)
        self.generation_worker.finished.connect(self._on_generation_finished)
        self.generation_worker.error.connect(self._on_generation_error)
        self.generation_worker.start()

    def _start_batch_generation(self):
        """开始批量生成"""
        # 检查是否有正在运行的批量生成任务
        if self.batch_worker and self.batch_worker.isRunning():
            print("警告：检测到正在运行的批量生成任务，跳过本次请求")
            return

        # 准备批量配置
        batch_configs = []
        batch_count = self.batch_count_spin.value()
        
        # 获取当前选择
        current_target = self._get_selected_option(self.ai_target_radios)
        current_weather = self._get_selected_option(self.ai_weather_radios)
        current_scene = self._get_selected_option(self.ai_scene_radios)
        
        for i in range(batch_count):
            # 确定本次生成的参数 - 使用标识符而不是具体值
            if self.random_target_cb.isChecked():
                target = 'RANDOM'  # 使用标识符
            else:
                target = current_target
                
            if self.random_weather_cb.isChecked():
                weather = 'RANDOM'  # 使用标识符
            else:
                weather = current_weather
                
            if self.random_scene_cb.isChecked():
                scene = 'RANDOM'  # 使用标识符
            else:
                scene = current_scene
            
            # 种子处理
            if self.random_seed_cb.isChecked():
                seed = -1  # 随机种子标识符
            else:
                seed = self.seed_spin.value()
            
            config = {
                'military_target': target,
                'weather': weather,
                'scene': scene,
                'custom_prompt': self.ai_prompt_text.toPlainText().strip(),
                'num_images': self.num_images_spin.value(),
                'steps': self.steps_spin.value(),
                'cfg_scale': self.cfg_spin.value(),
                'seed': seed,
                'width': 512,
                'height': 512,
                'scheduler_name': self.sampler_combo.currentText(),
                'save_images': True,
                'generate_annotations': True,
                # GSA检测参数
                'enable_gsa_detection': self.enable_gsa_cb.isChecked(),
                'gsa_confidence': self.gsa_confidence,
                'gsa_nms_threshold': self.gsa_nms_threshold,
                'gsa_save_original': self.gsa_save_original,
                'gsa_save_annotated': self.gsa_save_annotated,
                # 新增优化参数
                'gsa_enable_multi_scale': True,
                'gsa_enable_enhancement': True,
                'gsa_preset': 'enhanced_detection'
            }

            # 添加当前模型信息到批量生成配置中
            current_model_key = self.current_model_info.get('model_key')
            if current_model_key:
                config['model_info'] = {
                    'model_key': current_model_key,
                    'model_description': self.available_models.get(current_model_key, {}).get('description', ''),
                    'target_size_bias': self.available_models.get(current_model_key, {}).get('target_size_bias', '')
                }

            batch_configs.append(config)
        
        # 禁用生成按钮和模型切换
        self.ai_generate_btn.setEnabled(False)
        self.ai_generate_btn.setText("批量生成中...")
        self.switch_model_btn.setEnabled(False)
        self.model_combo.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.batch_progress_bar.setVisible(True)
        self.progress_bar.setRange(0, batch_count)
        self.batch_progress_bar.setRange(0, batch_count)
        
        # 清空当前图片列表
        self.current_images = []
        self.current_image_index = 0
        self._update_image_display()
        
        # 启动批量生成工作线程
        self.batch_worker = BatchGenerationWorker(self.ai_client, batch_configs)
        self.batch_worker.progress.connect(self._on_batch_progress)
        self.batch_worker.batch_progress.connect(self._on_batch_progress_update)
        self.batch_worker.finished.connect(self._on_batch_finished)
        self.batch_worker.error.connect(self._on_batch_error)
        self.batch_worker.image_generated.connect(self._on_batch_image_generated)
        self.batch_worker.start()

    def _on_batch_progress(self, message, current, total):
        """批量生成进度更新"""
        self.status_label.setText(message)
        self.progress_label.setText(f"总进度: {current}/{total}")

    def _on_batch_progress_update(self, current, total):
        """批量进度条更新"""
        self.batch_progress_bar.setValue(current)
        self.batch_progress_label.setText(f"批次进度: {current}/{total}")

    def _on_batch_image_generated(self, result):
        """批量生成中单张图片完成"""
        images = result.get('images', [])
        for image_info in images:
            if image_info.get('file_path'):
                self.current_images.append(image_info)
        
        # 如果是第一张图片，立即显示
        if len(self.current_images) == 1:
            self.current_image_index = 0
            self._update_image_display()

    def _on_batch_finished(self, result):
        """批量生成完成"""
        # 清理工作线程
        if self.batch_worker:
            self.batch_worker.deleteLater()
            self.batch_worker = None

        self.progress_bar.setVisible(False)
        self.batch_progress_bar.setVisible(False)
        self.ai_generate_btn.setEnabled(True)
        self.ai_generate_btn.setText("批量生成图像")

        # 重新启用模型切换控件
        self.model_combo.setEnabled(True)
        self._on_model_selection_changed()  # 更新切换按钮状态
        
        total_configs = result.get('total_configs', 0)
        successful_count = result.get('successful_count', 0)
        failed_count = result.get('failed_count', 0)
        
        self.status_label.setText(f"批量生成完成！成功: {successful_count}, 失败: {failed_count}")
        self.progress_label.setText(f"总进度: {total_configs}/{total_configs}")
        self.batch_progress_label.setText(f"批次进度: {total_configs}/{total_configs}")
        
        # 更新图片显示
        if self.current_images:
            self.current_image_index = 0
            self._update_image_display()
        
        QMessageBox.information(
            self, "批量生成完成", 
            f"批量生成完成！\n成功生成: {successful_count} 组\n失败: {failed_count} 组\n总图片数: {len(self.current_images)}"
        )

    def _on_batch_error(self, error_message):
        """批量生成错误"""
        # 清理工作线程
        if self.batch_worker:
            self.batch_worker.deleteLater()
            self.batch_worker = None

        self.progress_bar.setVisible(False)
        self.batch_progress_bar.setVisible(False)
        self.ai_generate_btn.setEnabled(True)
        self.ai_generate_btn.setText("批量生成图像")

        # 重新启用模型切换控件
        self.model_combo.setEnabled(True)
        self._on_model_selection_changed()  # 更新切换按钮状态

        self.status_label.setText(f"批量生成失败: {error_message}")
        QMessageBox.critical(self, "批量生成失败", f"批量生成失败:\n{error_message}")

    def _on_generation_progress(self, message):
        """生成进度更新"""
        # 如果启用了GSA检测，在进度消息中体现
        if hasattr(self, 'enable_gsa_cb') and self.enable_gsa_cb.isChecked():
            if "生成中" in message:
                message += " (将进行GSA检测)"
            elif "生成完成" in message and "检测" not in message:
                message += " - 正在进行GSA检测..."
        
        self.status_label.setText(message)

    def _on_generation_finished(self, result):
        """生成完成"""
        # 清理工作线程
        if self.generation_worker:
            self.generation_worker.deleteLater()
            self.generation_worker = None

        self.progress_bar.setVisible(False)
        self.ai_generate_btn.setEnabled(True)
        self.ai_generate_btn.setText("生成图像")

        # 重新启用模型切换控件
        self.model_combo.setEnabled(True)
        self._on_model_selection_changed()  # 更新切换按钮状态

        generation_id = result.get('generation_id', '')
        images = result.get('images', [])
        gsa_enabled = result.get('gsa_detection_enabled', False)
        gsa_results = result.get('gsa_results', {})
        
        if images:
            # 构建状态消息
            status_msg = f"生成完成！生成ID: {generation_id}"
            if gsa_enabled:
                detected_count = gsa_results.get('total_detections', 0)
                status_msg += f" | GSA检测: {detected_count}个目标"

            self.status_label.setText(status_msg)
            self.progress_label.setText(f"进度: {len(images)}/{len(images)}")

            # 更新图片列表
            self.current_images = [img for img in images if img.get('file_path')]
            self.current_image_index = 0
            self._update_image_display()

            # 构建成功消息
            success_msg = f"成功生成 {len(images)} 张图像！"
            if gsa_enabled:
                success_msg += f"\n\nGSA检测结果："
                success_msg += f"\n• 检测到 {gsa_results.get('total_detections', 0)} 个目标"
                if gsa_results.get('detection_summary'):
                    for target_type, count in gsa_results['detection_summary'].items():
                        success_msg += f"\n• {target_type}: {count}个"

                if gsa_results.get('annotated_images_saved'):
                    success_msg += f"\n• 已保存标注图片"
                if gsa_results.get('original_images_saved'):
                    success_msg += f"\n• 已保存原始图片"
            
            NotificationManager.show_success(self, "生成完成", success_msg)
        else:
            self.status_label.setText("生成完成，但未获得图像")

    def _on_generation_error(self, error_message):
        """生成错误"""
        # 清理工作线程
        if self.generation_worker:
            self.generation_worker.deleteLater()
            self.generation_worker = None

        self.progress_bar.setVisible(False)
        self.ai_generate_btn.setEnabled(True)
        self.ai_generate_btn.setText("生成图像")

        # 重新启用模型切换控件
        self.model_combo.setEnabled(True)
        self._on_model_selection_changed()  # 更新切换按钮状态

        self.status_manager.set_status(f"生成失败: {error_message}")
        self.error_handler.handle_error("generation", error_message, self)

    def _show_previous_image(self):
        """显示上一张图片"""
        if self.current_images and self.current_image_index > 0:
            self.current_image_index -= 1
            self._update_image_display()

    def _show_next_image(self):
        """显示下一张图片"""
        if self.current_images and self.current_image_index < len(self.current_images) - 1:
            self.current_image_index += 1
            self._update_image_display()

    def _update_image_display(self):
        """更新图片显示"""
        if not self.current_images:
            self.preview_label.clear()
            self.preview_label.setText("生成的图像将在此处显示")
            self.image_info_label.setText("0/0")
            self.prev_btn.setEnabled(False)
            self.next_btn.setEnabled(False)
            return
        
        # 显示当前图片
        current_image = self.current_images[self.current_image_index]
        file_path = current_image.get('file_path')
        
        # 调试信息
        print(f"尝试显示图片: {file_path}")
        print(f"文件是否存在: {os.path.exists(file_path) if file_path else 'None'}")
        
        if file_path:
            # 处理路径格式 - 改进的路径处理逻辑
            abs_path = self._normalize_image_path(file_path)
            
            print(f"标准化后的路径: {abs_path}")
            
            if os.path.exists(abs_path):
                try:
                    pixmap = QPixmap(abs_path)
                    if not pixmap.isNull():
                        # 获取预览区域的实际大小
                        label_size = self.preview_label.size()
                        # 确保有合理的最小尺寸
                        if label_size.width() < 100 or label_size.height() < 100:
                            label_size = self.preview_label.sizeHint()
                        
                        # 缩放图像以适应预览区域
                        scaled_pixmap = pixmap.scaled(
                            label_size,
                            Qt.AspectRatioMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation
                        )
                        self.preview_label.setPixmap(scaled_pixmap)
                        self.preview_label.setText("")
                        print(f"图片显示成功: {abs_path}")
                    else:
                        error_msg = f"无法加载图片: {abs_path}"
                        print(error_msg)
                        self.preview_label.setText(error_msg)
                except Exception as e:
                    error_msg = f"显示图像失败: {str(e)}"
                    print(error_msg)
                    self.preview_label.setText(error_msg)
            else:
                error_msg = f"图片文件不存在: {abs_path}"
                print(error_msg)
                self.preview_label.setText(error_msg)
        else:
            self.preview_label.setText("图片路径为空")
        
        # 更新导航信息
        total_images = len(self.current_images)
        current_num = self.current_image_index + 1
        self.image_info_label.setText(f"{current_num}/{total_images}")
        
        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_image_index > 0)
        self.next_btn.setEnabled(self.current_image_index < total_images - 1)

    def _normalize_image_path(self, file_path):
        """标准化图片路径"""
        if not file_path:
            return ""
        
        # 如果已经是绝对路径，直接返回
        if os.path.isabs(file_path):
            return file_path
        
        # 处理相对路径
        # 尝试不同的基础路径
        possible_bases = [
            "",  # 当前工作目录
            "../",  # 上级目录
            "backend/",  # backend目录
            "../backend/",  # 上级的backend目录
        ]
        
        for base in possible_bases:
            test_path = os.path.join(base, file_path)
            abs_test_path = os.path.abspath(test_path)
            if os.path.exists(abs_test_path):
                print(f"找到图片文件: {abs_test_path}")
                return abs_test_path
        
        # 如果都找不到，返回原始的绝对路径
        return os.path.abspath(file_path)

    def _test_image_display(self):
        """测试图片显示功能"""
        # 查找已生成的图片文件
        test_paths = [
            "data/generated/ai_generated/",
            "../data/generated/ai_generated/",
            "backend/data/generated/ai_generated/",
            "../backend/data/generated/ai_generated/"
        ]
        
        found_images = []
        for base_path in test_paths:
            abs_base_path = os.path.abspath(base_path)
            print(f"检查路径: {abs_base_path}")
            if os.path.exists(abs_base_path):
                print(f"路径存在，查找PNG文件...")
                for file in os.listdir(abs_base_path):
                    if file.endswith('.png'):
                        full_path = os.path.join(abs_base_path, file)
                        found_images.append({
                            'file_path': full_path,
                            'filename': file
                        })
                        print(f"找到图片: {file}")
                        if len(found_images) >= 5:  # 最多找5张测试
                            break
                break
        
        if found_images:
            self.current_images = found_images
            self.current_image_index = 0
            self._update_image_display()
            self.status_label.setText(f"找到 {len(found_images)} 张测试图片")
            print(f"测试图片加载完成，共 {len(found_images)} 张")
        else:
            self.status_label.setText("未找到测试图片")
            print("未找到任何测试图片")
            QMessageBox.information(self, "测试", "未找到已生成的图片文件进行测试")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 检查是否有正在运行的任务
        running_tasks = []
        if self.generation_worker and self.generation_worker.isRunning():
            running_tasks.append("单张生成")
        if self.batch_worker and self.batch_worker.isRunning():
            running_tasks.append("批量生成")
        if self.model_switch_worker and self.model_switch_worker.isRunning():
            running_tasks.append("模型切换")
        if self.model_preload_worker and self.model_preload_worker.isRunning():
            running_tasks.append("模型预加载")
        
        if running_tasks:
            reply = QMessageBox.question(
                self, "确认退出", 
                f"以下任务正在进行中：{', '.join(running_tasks)}\n确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
            
            # 终止所有工作线程
            if self.generation_worker and self.generation_worker.isRunning():
                self.generation_worker.terminate()
                self.generation_worker.wait()
            if self.batch_worker and self.batch_worker.isRunning():
                self.batch_worker.terminate()
                self.batch_worker.wait()
            if self.model_switch_worker and self.model_switch_worker.isRunning():
                self.model_switch_worker.terminate()
                self.model_switch_worker.wait()
            if self.model_preload_worker and self.model_preload_worker.isRunning():
                self.model_preload_worker.terminate()
                self.model_preload_worker.wait()

        # 停止预加载定时器
        if hasattr(self, 'preload_timer'):
            self.preload_timer.stop()
        
        if hasattr(self, 'ai_client'):
            self.ai_client.close()

        if hasattr(self, 'traditional_client'):
            self.traditional_client.close()

        event.accept()

    def _test_image_display(self):
        """测试图像显示功能"""
        try:
            # 创建一个测试图像
            from PIL import Image, ImageDraw
            import tempfile
            import os

            # 创建测试图像
            img = Image.new('RGB', (400, 300), color='lightblue')
            draw = ImageDraw.Draw(img)
            draw.rectangle([50, 50, 350, 250], fill='white', outline='black', width=2)
            draw.text((200, 150), "测试图像", fill='black', anchor='mm')

            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
                img.save(tmp.name)
                temp_path = tmp.name

            # 显示图像
            self.current_images = [temp_path]
            self.current_image_index = 0
            self._update_image_display()

            # 清理临时文件
            def cleanup():
                try:
                    os.unlink(temp_path)
                except:
                    pass

            # 延迟清理
            QTimer.singleShot(5000, cleanup)

        except Exception as e:
            self._show_error(f"测试图像显示失败: {str(e)}")

    def _generate_traditional_images(self):
        """生成传统合成图像"""
        try:
            # 获取选择的参数
            target = self._get_selected_radio_value(self.trad_target_radios)
            weather = self._get_selected_radio_value(self.trad_weather_radios)
            scene = self._get_selected_radio_value(self.trad_scene_radios)

            if not all([target, weather, scene]):
                self._show_error("请选择军事目标、天气条件和地形场景")
                return

            # 获取配置参数
            config = self._get_traditional_config()

            # 添加必需的三个参数
            config.update({
                'military_target': target,
                'weather': weather,
                'scene': scene
            })

            # 禁用生成按钮
            self.trad_generate_btn.setEnabled(False)
            self.trad_generate_btn.setText("合成中...")

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("正在准备传统合成...")

            # 启动传统生成工作线程
            self.traditional_worker = TraditionalGenerationWorker(self.traditional_client, config)
            self.traditional_worker.progress.connect(self._on_traditional_progress)
            self.traditional_worker.progress_value.connect(self._on_traditional_progress_value)
            self.traditional_worker.finished.connect(self._on_traditional_finished)
            self.traditional_worker.error.connect(self._on_traditional_error)
            self.traditional_worker.start()

        except Exception as e:
            self._show_error(f"启动传统生成失败: {str(e)}")
            self._reset_traditional_ui()

    def _on_traditional_progress(self, message):
        """传统生成进度更新"""
        self.status_label.setText(message)

    def _on_traditional_progress_value(self, value):
        """传统生成进度值更新"""
        self.progress_bar.setValue(value)

    def _on_traditional_finished(self, result):
        """传统生成完成"""
        self._reset_traditional_ui()

        if result.get("success"):
            data = result.get("data", {})
            results = data.get("results", [])
            total_generated = data.get("total_generated", 0)

            self.status_label.setText(f"传统合成完成！生成了 {total_generated} 张图像")

            # 更新图像显示
            if results:
                self.current_images = results
                self.current_image_index = 0
                self._update_image_display()

            # 显示成功消息
            QMessageBox.information(
                self, "生成完成",
                f"传统合成成功完成！\n生成了 {total_generated} 张图像"
            )
        else:
            error_message = result.get("message", "未知错误")
            self.status_label.setText(f"传统合成失败: {error_message}")
            QMessageBox.critical(self, "生成失败", f"传统合成失败:\n{error_message}")

    def _on_traditional_error(self, error_message):
        """传统生成错误"""
        self._reset_traditional_ui()
        self.status_manager.set_status(f"传统合成失败: {error_message}")
        self.error_handler.handle_error("generation", error_message, self)

    def _simulate_traditional_completion(self, config):
        """模拟传统生成完成（临时方法）"""
        self._reset_traditional_ui()
        self.status_label.setText("传统合成功能正在开发中...")
        self._show_info("传统合成功能正在开发中，敬请期待！")

    def _get_traditional_config(self) -> dict:
        """获取简化的传统生成配置"""
        # 映射抠图方法
        matting_method_map = {
            "自动选择": "auto",
            "深度学习(rembg)": "rembg",
            "传统算法(GrabCut)": "grabcut"
        }

        return {
            'num_images': self.trad_num_images_spin.value(),
            'target_scale': self.target_scale_slider.value() / 100.0,
            'opacity': self.opacity_slider.value() / 100.0,
            'random_position': self.random_position_cb.isChecked(),
            'add_shadow': self.add_shadow_cb.isChecked(),
            'shadow_offset': (5, 5),
            'shadow_blur': 3,
            'shadow_opacity': 0.3,
            'enable_matting': self.enable_matting_cb.isChecked(),
            'matting_method': matting_method_map.get(self.matting_method_combo.currentText(), 'auto')
        }

    def _reset_traditional_ui(self):
        """重置传统生成UI状态"""
        self.trad_generate_btn.setEnabled(True)
        self.trad_generate_btn.setText("开始合成")
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)

        # 清理工作线程
        if self.traditional_worker:
            if self.traditional_worker.isRunning():
                self.traditional_worker.cancel()
                self.traditional_worker.wait(3000)  # 等待最多3秒
            self.traditional_worker.deleteLater()
            self.traditional_worker = None

    def _get_selected_radio_value(self, radio_buttons: list) -> str:
        """获取选中的单选按钮值"""
        for radio in radio_buttons:
            if radio.isChecked():
                return radio.text()
        return ""

    def _show_error(self, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)

    def _show_warning(self, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, "警告", message)

    def _show_info(self, message: str):
        """显示信息消息"""
        QMessageBox.information(self, "信息", message)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyleSheet(MINIMAL_STYLE)
    main_win = MainWindow()
    main_win.show()
    sys.exit(app.exec())
