"""
随机对象生成器
为AI图像生成添加随机对象，增强提示词的多样性和真实感
"""

import random
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ObjectCategory(Enum):
    """对象类别枚举"""
    ENVIRONMENT = "environment"  # 环境对象
    DECORATION = "decoration"   # 装饰对象
    BACKGROUND = "background"   # 背景对象
    WEATHER_EFFECT = "weather_effect"  # 天气效果

@dataclass
class RandomObject:
    """随机对象定义"""
    name: str
    prompt_text: str
    category: ObjectCategory
    base_weight: float = 1.0
    scene_weights: Dict[str, float] = None
    weather_weights: Dict[str, float] = None
    vehicle_weights: Dict[str, float] = None
    conflicts_with: List[str] = None  # 冲突对象列表
    
    def __post_init__(self):
        if self.scene_weights is None:
            self.scene_weights = {}
        if self.weather_weights is None:
            self.weather_weights = {}
        if self.vehicle_weights is None:
            self.vehicle_weights = {}
        if self.conflicts_with is None:
            self.conflicts_with = []

class RandomObjectGenerator:
    """随机对象生成器"""
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化随机对象生成器
        
        Args:
            seed: 随机种子，None表示使用系统时间
        """
        self.seed = seed
        self.rng = random.Random(seed)
        self.objects_pool = self._init_objects_pool()
        self.generation_config = self._init_generation_config()
        logger.info(f"随机对象生成器已初始化，种子: {seed}")
    
    def _init_objects_pool(self) -> List[RandomObject]:
        """初始化随机对象池"""
        objects = []
        
        # 环境对象
        objects.extend([
            RandomObject(
                name="debris",
                prompt_text="scattered debris, rubble",
                category=ObjectCategory.ENVIRONMENT,
                base_weight=0.8,
                scene_weights={"城市": 1.5, "岛屿": 0.5, "乡村": 0.8}
            ),
            RandomObject(
                name="trees",
                prompt_text="distant trees, forest background",
                category=ObjectCategory.ENVIRONMENT,
                base_weight=1.0,
                scene_weights={"城市": 0.3, "岛屿": 1.2, "乡村": 1.8}
            ),
            RandomObject(
                name="buildings",
                prompt_text="background buildings, urban structures",
                category=ObjectCategory.ENVIRONMENT,
                base_weight=1.0,
                scene_weights={"城市": 2.0, "岛屿": 0.2, "乡村": 0.4}
            ),
            RandomObject(
                name="rocks",
                prompt_text="rocky terrain, stone formations",
                category=ObjectCategory.ENVIRONMENT,
                base_weight=0.9,
                scene_weights={"城市": 0.3, "岛屿": 1.5, "乡村": 1.0}
            ),
            RandomObject(
                name="water_body",
                prompt_text="water in background, distant lake",
                category=ObjectCategory.ENVIRONMENT,
                base_weight=0.7,
                scene_weights={"城市": 0.5, "岛屿": 1.8, "乡村": 1.0}
            )
        ])
        
        # 装饰对象
        objects.extend([
            RandomObject(
                name="smoke",
                prompt_text="smoke plumes, exhaust smoke",
                category=ObjectCategory.DECORATION,
                base_weight=1.2,
                vehicle_weights={"坦克": 1.5, "战机": 1.8, "舰艇": 1.0}
            ),
            RandomObject(
                name="dust_cloud",
                prompt_text="dust clouds, kicked up dirt",
                category=ObjectCategory.DECORATION,
                base_weight=1.0,
                vehicle_weights={"坦克": 1.8, "战机": 0.3, "舰艇": 0.1},
                weather_weights={"雨天": 0.3, "雪天": 0.2, "大雾": 0.5, "夜间": 0.8}
            ),
            RandomObject(
                name="sparks",
                prompt_text="metal sparks, welding sparks",
                category=ObjectCategory.DECORATION,
                base_weight=0.6,
                vehicle_weights={"坦克": 1.2, "战机": 0.8, "舰艇": 1.0}
            ),
            RandomObject(
                name="steam",
                prompt_text="steam vapor, hot exhaust",
                category=ObjectCategory.DECORATION,
                base_weight=0.8,
                weather_weights={"雨天": 1.5, "雪天": 2.0, "大雾": 1.8, "夜间": 1.2}
            )
        ])
        
        # 背景对象
        objects.extend([
            RandomObject(
                name="birds",
                prompt_text="birds in sky, flying birds",
                category=ObjectCategory.BACKGROUND,
                base_weight=0.5,
                weather_weights={"雨天": 0.2, "雪天": 0.1, "大雾": 0.3, "夜间": 0.1},
                conflicts_with=["aircraft_formation"]
            ),
            RandomObject(
                name="clouds",
                prompt_text="dramatic clouds, cloud formations",
                category=ObjectCategory.BACKGROUND,
                base_weight=1.0,
                weather_weights={"雨天": 1.8, "雪天": 1.5, "大雾": 0.2, "夜间": 0.8}
            ),
            RandomObject(
                name="distant_vehicles",
                prompt_text="distant vehicles, background traffic",
                category=ObjectCategory.BACKGROUND,
                base_weight=0.7,
                scene_weights={"城市": 1.5, "岛屿": 0.2, "乡村": 0.8}
            ),
            RandomObject(
                name="aircraft_formation",
                prompt_text="aircraft formation in distance",
                category=ObjectCategory.BACKGROUND,
                base_weight=0.4,
                vehicle_weights={"战机": 1.5, "坦克": 0.8, "舰艇": 1.0},
                conflicts_with=["birds"]
            )
        ])
        
        # 天气效果增强
        objects.extend([
            RandomObject(
                name="rain_effects",
                prompt_text="heavy rain effects, water splashing",
                category=ObjectCategory.WEATHER_EFFECT,
                base_weight=1.5,
                weather_weights={"雨天": 2.0, "雪天": 0.0, "大雾": 0.3, "夜间": 0.8}
            ),
            RandomObject(
                name="snow_effects",
                prompt_text="heavy snowfall, snow accumulation",
                category=ObjectCategory.WEATHER_EFFECT,
                base_weight=1.5,
                weather_weights={"雨天": 0.0, "雪天": 2.0, "大雾": 0.2, "夜间": 0.8}
            ),
            RandomObject(
                name="fog_effects",
                prompt_text="thick fog layers, misty atmosphere",
                category=ObjectCategory.WEATHER_EFFECT,
                base_weight=1.5,
                weather_weights={"雨天": 0.5, "雪天": 0.3, "大雾": 2.0, "夜间": 1.0}
            ),
            RandomObject(
                name="lighting_effects",
                prompt_text="dramatic lighting, light rays",
                category=ObjectCategory.WEATHER_EFFECT,
                base_weight=1.0,
                weather_weights={"雨天": 0.8, "雪天": 1.2, "大雾": 1.5, "夜间": 1.8}
            )
        ])
        
        return objects
    
    def _init_generation_config(self) -> Dict[str, Any]:
        """初始化生成配置"""
        return {
            "max_objects_per_prompt": 3,
            "min_objects_per_prompt": 0,
            "object_probability": 0.7,  # 添加对象的概率
            "category_limits": {
                ObjectCategory.ENVIRONMENT: 2,
                ObjectCategory.DECORATION: 2,
                ObjectCategory.BACKGROUND: 1,
                ObjectCategory.WEATHER_EFFECT: 1
            }
        }
    
    def set_seed(self, seed: Optional[int]):
        """设置随机种子"""
        self.seed = seed
        self.rng = random.Random(seed)
        logger.debug(f"随机对象生成器种子已更新: {seed}")
    
    def calculate_object_weight(
        self,
        obj: RandomObject,
        vehicle: str,
        weather: str,
        scene: str
    ) -> float:
        """
        计算对象的权重
        
        Args:
            obj: 随机对象
            vehicle: 载具类型
            weather: 天气条件
            scene: 场景环境
            
        Returns:
            float: 计算后的权重
        """
        weight = obj.base_weight
        
        # 应用场景权重
        if scene in obj.scene_weights:
            weight *= obj.scene_weights[scene]
        
        # 应用天气权重
        if weather in obj.weather_weights:
            weight *= obj.weather_weights[weather]
        
        # 应用载具权重
        if vehicle in obj.vehicle_weights:
            weight *= obj.vehicle_weights[vehicle]
        
        return max(0.0, weight)  # 确保权重非负

    def generate_random_objects(
        self,
        vehicle: str,
        weather: str,
        scene: str,
        max_objects: Optional[int] = None
    ) -> List[str]:
        """
        生成随机对象列表

        Args:
            vehicle: 载具类型
            weather: 天气条件
            scene: 场景环境
            max_objects: 最大对象数量，None使用配置默认值

        Returns:
            List[str]: 随机对象的提示词列表
        """
        if max_objects is None:
            max_objects = self.generation_config["max_objects_per_prompt"]

        # 检查是否应该添加对象
        if self.rng.random() > self.generation_config["object_probability"]:
            return []

        # 计算所有对象的权重
        weighted_objects = []
        for obj in self.objects_pool:
            weight = self.calculate_object_weight(obj, vehicle, weather, scene)
            if weight > 0:
                weighted_objects.append((obj, weight))

        if not weighted_objects:
            return []

        # 按类别分组
        objects_by_category = {}
        for obj, weight in weighted_objects:
            category = obj.category
            if category not in objects_by_category:
                objects_by_category[category] = []
            objects_by_category[category].append((obj, weight))

        # 选择对象
        selected_objects = []
        selected_names = set()

        # 确定要生成的对象数量
        num_objects = self.rng.randint(
            self.generation_config["min_objects_per_prompt"],
            max_objects
        )

        for _ in range(num_objects):
            if len(selected_objects) >= max_objects:
                break

            # 选择一个类别
            available_categories = []
            for category, objects in objects_by_category.items():
                category_limit = self.generation_config["category_limits"].get(category, 1)
                current_count = sum(1 for obj in selected_objects if obj.category == category)
                if current_count < category_limit and objects:
                    available_categories.append(category)

            if not available_categories:
                break

            category = self.rng.choice(available_categories)
            category_objects = objects_by_category[category]

            # 过滤掉已选择的对象和冲突对象
            available_objects = []
            for obj, weight in category_objects:
                if obj.name in selected_names:
                    continue

                # 检查冲突
                has_conflict = False
                for selected_obj in selected_objects:
                    if (obj.name in selected_obj.conflicts_with or
                        selected_obj.name in obj.conflicts_with):
                        has_conflict = True
                        break

                if not has_conflict:
                    available_objects.append((obj, weight))

            if not available_objects:
                continue

            # 加权随机选择
            total_weight = sum(weight for _, weight in available_objects)
            if total_weight <= 0:
                continue

            rand_val = self.rng.random() * total_weight
            cumulative_weight = 0

            for obj, weight in available_objects:
                cumulative_weight += weight
                if rand_val <= cumulative_weight:
                    selected_objects.append(obj)
                    selected_names.add(obj.name)
                    break

        # 返回提示词文本
        return [obj.prompt_text for obj in selected_objects]

    def get_object_statistics(self) -> Dict[str, Any]:
        """获取对象池统计信息"""
        stats = {
            "total_objects": len(self.objects_pool),
            "by_category": {},
            "object_names": []
        }

        for obj in self.objects_pool:
            category_name = obj.category.value
            if category_name not in stats["by_category"]:
                stats["by_category"][category_name] = 0
            stats["by_category"][category_name] += 1
            stats["object_names"].append(obj.name)

        return stats

    def set_seed(self, seed: Optional[int]):
        """设置随机种子"""
        self.seed = seed
        self.rng = random.Random(seed)
        logger.info(f"随机对象生成器种子已设置: {seed}")

    def update_generation_config(self, config: Dict[str, Any]):
        """更新生成配置"""
        self.generation_config.update(config)
        logger.info(f"随机对象生成配置已更新: {config}")

    def add_custom_object(self, obj: RandomObject):
        """添加自定义对象"""
        self.objects_pool.append(obj)
        logger.info(f"已添加自定义对象: {obj.name}")

    def remove_object(self, object_name: str) -> bool:
        """移除对象"""
        for i, obj in enumerate(self.objects_pool):
            if obj.name == object_name:
                del self.objects_pool[i]
                logger.info(f"已移除对象: {object_name}")
                return True
        return False

    def get_objects_for_context(
        self,
        vehicle: str,
        weather: str,
        scene: str,
        include_weights: bool = False
    ) -> List[Dict[str, Any]]:
        """
        获取特定上下文下的对象信息

        Args:
            vehicle: 载具类型
            weather: 天气条件
            scene: 场景环境
            include_weights: 是否包含权重信息

        Returns:
            List[Dict]: 对象信息列表
        """
        result = []
        for obj in self.objects_pool:
            weight = self.calculate_object_weight(obj, vehicle, weather, scene)
            if weight > 0:
                obj_info = {
                    "name": obj.name,
                    "prompt_text": obj.prompt_text,
                    "category": obj.category.value
                }
                if include_weights:
                    obj_info["weight"] = weight
                result.append(obj_info)

        return result
